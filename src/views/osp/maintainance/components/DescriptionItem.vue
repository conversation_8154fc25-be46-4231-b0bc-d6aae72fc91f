<template>
    <div class="description-item" :class="{
        'is-bordered': parentBordered,
        [`span-${span}`]: true,
        'is-full-span': parentBordered && isFullSpan
    }">
        <div class="item-container">
            <span v-if="label" class="item-label">{{ label }}：</span>
            <span class="item-content">
                <slot></slot>
            </span>
        </div>
    </div>
</template>

<script>
export default {
    name: 'DescriptionItem',
    inject: {
        descriptions: {
            default: () => ({})
        }
    },
    props: {
        label: {
            type: String,
            default: ''
        },
        span: {
            type: Number,
            default: 1
        }
    },
    computed: {
        parentBordered() {
            return this.descriptions.bordered;
        },
        parentSize() {
            return this.descriptions.size || 'default';
        },
        parentColumn() {
            return this.descriptions.column || 1;
        },
        isFullSpan() {
            // 判断当前 item 是否占据了父 Descriptions 组件定义的全部列数
            // 或者更简单地，如果 span 大于等于 column，就认为是 full span
            return this.span >= (this.descriptions.column || 1);
        }
    }
};
</script>

<style lang="scss" scoped>
.description-item {
    $border-color: #e8eaec; // 与父组件同步
    $label-bg-color: #f8f8f9; // 与父组件同步
    $label-text-color: #909399; // bordered 模式下标签颜色，类似 Element
    $content-text-color: #515a6e; // 常规内容颜色，与父组件同步
    $base-padding-vertical: 12px;
    $base-padding-horizontal: 16px; // 水平 padding 可稍大
    $small-padding-vertical: 8px;
    $small-padding-horizontal: 12px;
    $large-padding-vertical: 16px;
    $large-padding-horizontal: 20px;

    font-size: inherit; // 继承父组件字体大小
    line-height: 1.5;
    color: $content-text-color; // 默认内容颜色

    // 跨列设置
    @for $i from 1 through 12 {

        // 假设最大列数为 12
        &.span-#{$i} {
            grid-column: span #{$i} / auto;
        }
    }

    .item-container {
        // 非 bordered 模式下由父组件的 gap 控制间距
        display: inline-flex; // 保持 inline-flex
        align-items: start;
        width: 100%;
    }

    .item-label {
        margin-right: 8px;
        // 非 bordered 模式下颜色继承父组件 primary text color (默认#17233d) 或保持 #515a6e
        // color: inherit; // 或指定颜色
        font-weight: normal; // 非 bordered 模式下标签不加粗，模拟 Element
        white-space: nowrap;
        flex-shrink: 0;

        // 非 bordered 模式下显示冒号
        &:not(.is-bordered-label):after {
            content: "：";
        }
    }

    .item-content {
        flex: 1;
        word-break: break-word; // 允许长单词换行
        min-width: 0; // 配合 flex:1 和 overflow:hidden 使用，确保在 flex item 中 ellipsis 生效
    }

    // 边框模式下的特定样式
    &.is-bordered {
        // 使用父组件传递的 size 来决定 padding
        $padding-vertical: $base-padding-vertical;
        $padding-horizontal: $base-padding-horizontal;

        // 根据父组件的 size 调整自身的 padding 变量
        // 注意：这里不能直接修改 props，而是通过 computed property parentSize 来影响样式决策
        // 但由于 SCSS 变量作用域，更直接的方式是在使用处判断

        // item 自身不再有外边距，由 grid 控制
        padding: 0;
        border-bottom: 1px solid $border-color;
        border-right: 1px solid $border-color;


        .item-container {
            display: flex;
            width: 100%; // 确保 item-container 占满 description-item
            align-items: stretch; // 新增：确保标签和高内容区域顶部对齐
            height: 100%;
        }

        .item-label {
            flex: 0 0 auto;
            width: 120px; // 默认标签宽度
            padding: $base-padding-vertical $base-padding-horizontal; // 默认padding
            margin: 0;
            color: $label-text-color;
            background-color: $label-bg-color;
            font-weight: normal;
            text-align: right;
            border-right: 1px solid $border-color;
            white-space: nowrap; // 确保标签不换行
            height: auto;

            &:after {
                content: "";
            }
        }

        .item-content {
            flex: 1 1 auto;
            padding: $base-padding-vertical $base-padding-horizontal; // 默认padding
            word-break: break-word; // 允许内容换行，但 nowrap 优先
            white-space: nowrap; // 防止内容换行
            overflow: hidden; // 隐藏超出部分
            text-overflow: ellipsis; // 用省略号表示超出
            min-width: 0; // 确保在 flex 布局中 ellipsis 生效
        }

        // 根据 size 调整 padding
        &.size-small {

            .item-label,
            .item-content {
                padding: $small-padding-vertical $small-padding-horizontal;
            }
        }

        &.size-large {

            .item-label,
            .item-content {
                padding: $large-padding-vertical $large-padding-horizontal;
            }
        }

        // 当 item 占据整行时 (isFullSpan)
        &.is-full-span {
            .item-label {
                // 如果希望标签宽度自适应或有不同表现，可以在这里修改
                // 例如，如果希望标签宽度不变，但移除与内容的分割线
                border-right: none; // 移除标签和内容间的分割线，使整行看起来更统一
                // 如果希望标签宽度也参与flex，则需要调整flex属性
            }

            .item-content {
                // 内容区应该能利用所有合并后的空间
                // white-space: normal; // 如果整行备注允许换行
                // overflow: visible;
                // text-overflow: clip;
                // 对于备注这种可能很长的内容，如果希望它能换行并占满，可以覆盖 nowrap
                white-space: normal; // 允许备注这类占满整行的内容换行
                overflow: visible; // 允许内容溢出（如果需要）
                text-overflow: clip; // 不需要省略号
            }
        }

        // 移除最后一个元素的右边框，如果它是列的末尾
        // 这个逻辑比较复杂，依赖于它在 grid 中的实际位置
        // 通常由父组件的 grid 边框处理，或者通过 :nth-child 结合 column 数来判断
    }

    // 非边框模式下的间距 (通过父组件的 gap 实现，这里无需处理)
}
</style>
