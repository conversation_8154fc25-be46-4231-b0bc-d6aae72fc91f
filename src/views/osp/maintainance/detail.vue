<template>
    <div class="fault-detail-page">
        <Button type="text" icon="md-undo" @click="$router.go(-1)">返回</Button>
        <!-- 故障工单信息 -->
        <Card class="section-card">
            <p slot="title">运维工单</p>
            <Descriptions :column="3" bordered>
                <DescriptionItem label="运维单号">{{ formData.orderCode || '-' }}</DescriptionItem>
                <DescriptionItem label="工单状态">{{ getDictLabel('work_order_status', formData.orderStatus) || '-' }}
                </DescriptionItem>
                <DescriptionItem label="工单类型">{{ getDictLabel('work_order_type', formData.orderType) }}
                </DescriptionItem>
                <DescriptionItem label="工单来源">{{ getDictLabel('work_order_source', formData.orderSource) }}
                </DescriptionItem>
                <DescriptionItem label="截止时间">{{ formData.deadline || '-' }}</DescriptionItem>
                <DescriptionItem label="下发方式">{{ getDictLabel('dispatch_mode', formData.dispatchMode) }}
                </DescriptionItem>
                <DescriptionItem label="审核权限">{{ getDictLabel('dispatch_review_permission',
                    formData.dispatchAuditPermission) }}</DescriptionItem>
                <DescriptionItem label="审核方式">{{ getDictLabel('review_mode', formData.auditMode) }}</DescriptionItem>
                <DescriptionItem label="是否超时">{{ typeof formData.overTime === 'boolean' ? (formData.overTime ? '是' :
                    '否') : '-' }}</DescriptionItem>
                <DescriptionItem label="创建时间">{{ formData.createdAt || '-' }}</DescriptionItem>
                <DescriptionItem label="创建人" :span="2">{{ formData.createdBy || '-' }}</DescriptionItem>
                <DescriptionItem label="备注" :span="3">{{ formData.remark || '-' }}</DescriptionItem>
            </Descriptions>
        </Card>

        <!-- 电站信息 -->
        <Card class="section-card">
            <p slot="title">电站信息</p>
            <Descriptions :column="2" bordered>
                <DescriptionItem label="电站编码">{{ formData.stationCode || '-' }}</DescriptionItem>
                <DescriptionItem label="逆变器SN码">
                    <a v-if="formData.inverterSn" type="text">
                        {{ formData.inverterSn }}
                    </a>
                    <span v-else>-</span>
                </DescriptionItem>
                <DescriptionItem label="业主姓名">{{ formData.stationName || '-' }}</DescriptionItem>
                <DescriptionItem label="手机号">{{ formData.stationPhone || '-' }}</DescriptionItem>
                <DescriptionItem label="电站模式">{{ formData.stationMode || '-' }}</DescriptionItem>
                <DescriptionItem label="关联资方">{{ formData.specialFlag || '-' }}</DescriptionItem>
                <DescriptionItem label="所属分中心">{{ formData.subCenterName || '-' }}</DescriptionItem>
                <DescriptionItem label="服务商类别">{{ formData.opType || '-' }}</DescriptionItem>
                <DescriptionItem label="运维商名称">{{ formData.opName || '-' }}</DescriptionItem>
                <!-- <DescriptionItem label="是否质保期内">{{ formData.isWarranty || '-' }}</DescriptionItem> -->
                <DescriptionItem label="区域">{{ (formData.provinceName || '') + (formData.cityName || '') +
                    (formData.regionName || '') || '-' }}</DescriptionItem>
                <DescriptionItem label="详细地址" :span="2">{{ formData.address || '-' }}</DescriptionItem>
            </Descriptions>
        </Card>

        <!-- 故障信息 -->
        <Card class="section-card">
            <p slot="title">故障信息</p>
            <Descriptions :column="2" bordered>
                <DescriptionItem label="故障名称">{{ formData.faultInfo?.faultName || '-' }}</DescriptionItem>
                <DescriptionItem label="故障等级">{{ getDictLabel('fault_level', formData.faultInfo?.faultLevel) }}
                </DescriptionItem>
                <DescriptionItem label="故障描述">{{ formData.faultInfo?.faultDetails || '-' }}</DescriptionItem>
                <DescriptionItem label="故障照片">
                    <template v-if="formData.faultInfo?.photos">
                        <img v-for="(photo, index) in formData.faultInfo.photos.split(',')" :key="index"
                            style="width: 100px; height: 100px; margin-right: 10px; cursor: pointer;" :src="photo"
                            @click="handlePreview(photo)" />
                    </template>
                    <span v-else>-</span>
                </DescriptionItem>
            </Descriptions>
        </Card>

        <!-- 流程信息 和 工单处理 Tab -->
        <Tabs v-model="activeTabName" type="card" class="section-card">
            <TabPane
                v-if="!['TO_HEAD_DISPATCH', 'TO_SUB_CENTER_DISPATCH', 'TO_ASSIGN', 'CLOSED'].includes(formData.orderStatus)"
                label="工单处理" name="workOrderHandle">
                <Card>
                    <Form ref="handleFormRef" :model="formData" :rules="handleFormRules" label-position="top">
                        <div v-for="(item, idx) in formData.handleCheckItems" :key="'cfg' + idx">
                            <FormItem v-if="item.resultType === 'text'" :label="item.checkItem"
                                :prop="'handleCheckItems.' + idx + '.resultContent'"
                                :rules="[{ required: true, message: `请输入${item.checkItem}`, trigger: 'blur' }]">
                                <Input v-model="item.resultContent" type="textarea" :rows="3"
                                    :placeholder="`请输入${item.checkItem}`" :disabled="!editable" />
                            </FormItem>

                            <FormItem v-if="item.resultType === 'select'" :label="item.checkItem"
                                :prop="'handleCheckItems.' + idx + '.resultContent'"
                                :rules="[{ required: true, message: `请选择${item.checkItem}`, trigger: 'change' }]">
                                <Select v-model="item.resultContent" :placeholder="`请选择${item.checkItem}`"
                                    :disabled="!editable">
                                    <Option value="良好">良好</Option>
                                    <Option value="损坏">损坏</Option>
                                </Select>
                            </FormItem>
                            <FormItem v-if="item.resultType === 'image'" :label="item.checkItem"
                                :prop="'handleCheckItems.' + idx + '.resultContent'" :rules="[{
                                    required: true, message: `请上传${item.checkItem}照片`, trigger: 'change', validator: (rule, value, callback) => {
                                        if (!value || (Array.isArray(value) && value.length === 0)) {
                                            callback(new Error(`请上传${item.checkItem}照片`));
                                        } else {
                                            callback();
                                        }
                                    }
                                }]">
                                <Row :gutter="20" style="width: 100%;">
                                    <Col :span="6">
                                    <FormItem label="示例照片">
                                        <img :src="item.exampleImage"
                                            style="width: 100px; height: 100px; background-color: #eee; object-fit: cover; cursor: pointer;"
                                            @click="handlePreview(item.exampleImage)" alt="示例图" />
                                    </FormItem>
                                    </Col>
                                    <Col :span="18">
                                    <FormItem label="上传照片" :required="false">
                                        <cusMultiUpload :imgUrl.sync="item.resultContent" :type="'image'"
                                            :canDel="editable" :disabled="!editable" />
                                    </FormItem>
                                    </Col>
                                </Row>
                            </FormItem>
                        </div>
                        <FormItem label="备注" prop="remark">
                            <Input v-model="formData.remark" type="textarea" :rows="3" placeholder="请输入备注"
                                :disabled="!editable" />
                        </FormItem>
                    </Form>
                </Card>
            </TabPane>
            <TabPane label="工单流程" name="processInfo">
                <Card v-if="Array.isArray(formData.processes) && formData.processes.length > 0">
                    <Timeline>
                        <TimelineItem v-for="(process, index) in formData.processes" :key="index">
                            <!-- <p class="time">{{ process.createdAt }}</p>
                            <p class="content">{{ process.processName }} - {{ process.createdBy }}</p> -->
                            <div class="flex-column">
                                <div>{{ process.processName }} - {{ process.createdBy }}</div>
                                <div v-if="process.handleCheckItemsJson" class="handle-check-items">
                                    <!-- <div class="check-items-title">处理检查项：</div> -->
                                    <Descriptions :column="1" bordered>
                                        <DescriptionItem v-for="(item, idx) in JSON.parse(process.handleCheckItemsJson)"
                                            :key="'check-' + idx" :label="item.checkItem">
                                            <template v-if="item.resultType === 'text' || item.resultType === 'select'">
                                                {{ item.resultContent || '-' }}
                                            </template>
                                            <template v-else-if="item.resultType === 'image' && item.resultContent">
                                                <img v-for="(img, imgIdx) in item.resultContent.split(',')"
                                                    :key="'img-' + imgIdx"
                                                    style="width: 80px; height: 80px; margin-right: 5px; cursor: pointer;"
                                                    :src="img" @click="handlePreview(img)" />
                                            </template>
                                        </DescriptionItem>
                                    </Descriptions>
                                </div>
                                <p class="time">{{ process.createdAt }}</p>
                            </div>
                        </TimelineItem>
                    </Timeline>
                </Card>
                <div v-else style="text-align: center; padding: 20px;">暂无工单流程</div>
            </TabPane>
        </Tabs>

        <!-- 底部操作按钮 -->
        <div class="action-buttons" v-if="!['CLOSED', 'FINISHED'].includes(formData.orderStatus) && action !== 'view'">
            <Button v-if="['TO_ASSIGN'].includes(formData.orderStatus)" type="success"
                @click="openAssignModal">指派</Button>
            <Button v-if="['TO_PROCESS'].includes(formData.orderStatus)" type="success"
                @click="handleSubmit">提交</Button>
        </div>

        <Modal title="图片预览" v-model="previewImageVisible">
            <img :src="previewImageUrl" v-if="previewImageVisible" style="width: 100%">
        </Modal>

        <!-- 选择服务兵弹窗 -->
        <SelectStaff :visible.sync="selectStaffModalVisible" :orderData="formData" @on-select="handleStaffAssigned" />
    </div>
</template>

<script>
import API from '@/api/maintainance';
import cusMultiUpload from '@/components/public/cusMultiUpload.vue';
import { mapActions, mapGetters } from 'vuex';
import Descriptions from './components/Descriptions.vue';
import DescriptionItem from './components/DescriptionItem.vue';
import SelectStaff from './components/SelectStaff.vue'; // 导入 SelectStaff 组件

export default {
    name: 'FaultDetail',
    components: {
        cusMultiUpload,
        Descriptions,
        DescriptionItem,
        SelectStaff, // 注册 SelectStaff 组件
    },
    data() {
        return {
            selectStaffModalVisible: false, // 控制 SelectStaff 弹窗的显示状态
            rejectOrderDialogVisible: false,
            previewImageVisible: false,
            previewImageUrl: '',
            action: this.$route.query.action,
            activeTabName: 'processInfo',
            formData: {
                address: '',
                auditMode: '',
                auditUnit: '',
                businessType: '',
                cityId: '',
                cityName: '',
                closeReason: '',
                closeTime: '',
                configCheckItems: [],
                configId: '',
                createdAt: '',
                createdBy: '',
                deadline: '',
                devices: {},
                dispatchAuditPermission: '',
                dispatchMode: '',
                dispatchUnit: '',
                dispatched: null,
                faultDescription: '',
                faultInfo: {},
                finishFlag: '',
                finishTime: '',
                firstAuditResult: '',
                handleCheckItems: [],
                handleTime: '',
                handler: '',
                id: '',
                inverterSn: '',
                isWarranty: null,
                opCode: '',
                opMemberId: '',
                opName: '',
                opType: '',
                orderCode: '',
                orderName: '',
                orderSource: '',
                orderStatus: '',
                orderType: '',
                overTime: null,
                processes: [],
                provinceId: '',
                provinceName: '',
                regionId: '',
                regionName: '',
                remark: '',
                secondAuditResult: '',
                sparePartApplyNo: '',
                sparePartAuditPassTime: '',
                sparePartAuditStatus: '',
                specialFlag: '',
                stationCode: '',
                stationMode: '',
                stationName: '',
                stationPhone: '',
                streetId: '',
                streetName: '',
                subCenterName: '',
            },
            handleFormRules: {}, // 初始化表单校验规则
        };
    },
    computed: {
        ...mapGetters('dict/maintainance', [
            'getDictByType',
            'getDictMapByType'
        ]),
        editable() {
            return ['TO_PROCESS'].includes(this.formData.orderStatus) && this.action !== 'view';
        },
    },
    methods: {
        ...mapActions('dict/maintainance', {
            fetchMaintainanceDict: 'fetchDict'
        }),
        getDictLabel(dictType, value) {
            const dictMap = this.getDictMapByType(dictType);
            return dictMap ? (dictMap[value] || '-') : '-';
        },
        async getDetail() {
            const orderCode = this.$route.query.orderCode;
            if (!orderCode) {
                this.$Message.error('缺少工单标识');
                this.$router.back();
                return;
            }
            this.formData.orderCode = orderCode;

            try {
                const res = (await API.getWorkOrderByOrderCode(orderCode)).data;
                if (res.success) {
                    Object.assign(this.formData, res.result);
                    if (!['TO_HEAD_DISPATCH', 'TO_SUB_CENTER_DISPATCH', 'TO_ASSIGN', 'CLOSED'].includes(this.formData.orderStatus)) {
                        this.activeTabName = 'workOrderHandle';
                    } else {
                        this.activeTabName = 'processInfo';
                    }
                    if (Array.isArray(this.formData.handleCheckItems) && this.formData.handleCheckItems.length === 0) {
                        this.formData.handleCheckItems = this.formData.configCheckItems?.map(item => ({ ...item, resultContent: item.resultType === 'image' ? [] : null })) || [];
                    } else {
                        this.formData.handleCheckItems = this.formData.handleCheckItems?.map(item => ({ ...item, resultContent: item.resultType === 'image' ? item.resultContent.split(',') : item.resultContent })) || [];
                    }
                    if (this.formData.createdAt) {
                        this.formData.createdAt = this.formData.createdAt.replace('T', ' ');
                    }
                } else {
                    this.$Message.error(res.error || '获取详情失败');
                }
            } catch (error) {
                console.error('获取详情失败:', error);
                this.$Message.error('获取详情数据时发生错误');
            }
        },
        async handleSubmit() {
            if (this.$refs.handleFormRef) {
                this.$refs.handleFormRef.validate(async (valid) => {
                    if (valid) {
                        this.showSubmitConfirmModal();
                    } else {
                        this.$Message.error('请检查表单填写是否正确！');
                    }
                });
            } else {
                // 如果表单引用不存在（例如“工单处理”标签页未激活或未渲染），直接显示确认框
                this.showSubmitConfirmModal();
            }
        },
        showSubmitConfirmModal() {
            this.$Modal.confirm({
                title: '提示',
                content: '确认提交工单处理结果吗?',
                onOk: async () => {
                    const params = {
                        orderCode: this.formData.orderCode,
                        handleCheckItems: this.formData.handleCheckItems?.map(item => {
                            if (item.resultType === 'image' && Array.isArray(item.resultContent)) {
                                return {
                                    ...item,
                                    resultContent: item.resultContent.join(','),
                                };
                            }
                            return item;
                        }) || [],
                        remark: this.formData.remark
                    };

                    try {
                        const res = (await API.handleWorkOrder(params)).data;
                        if (res.success) {
                            this.$Message.success('提交成功');
                            this.$router.back();
                        } else {
                            this.$Message.error(res.error || '提交失败');
                        }
                    } catch (error) {
                        console.error('提交失败:', error);
                        this.$Message.error('提交处理结果时发生错误');
                    }
                },
                onCancel: () => {
                    // 取消提交
                }
            });
        },
        handleCancel() {
            this.$router.back();
        },
        handlePreview(url) {
            this.previewImageUrl = url;
            this.previewImageVisible = true;
        },
        openAssignModal() {
            this.selectStaffModalVisible = true;
        },
        handleStaffAssigned() {
            // 指派成功后，SelectStaff 组件会自行关闭，并触发此事件
            // 此处返回列表页
            this.$Message.success('指派成功，即将返回列表页');
            setTimeout(() => {
                this.$router.back();
            }, 1500); // 延迟一点给用户看提示
        },
    },
    created() {
        this.getDetail();
        this.fetchMaintainanceDict([
            'fault_level',
            'work_order_type',
            'work_order_status',
            'dispatch_mode',
            'dispatch_review_permission',
            'review_mode',
            'work_order_source',
        ]);
    }
};
</script>

<style scoped lang="scss">
.fault-detail-page {
    background-color: white;
    padding: 16px;
}

.cus-back {
    margin-bottom: 16px;
}

.section-card {
    margin-bottom: 16px;
}

.card-header {
    font-weight: bold;
}

.action-buttons {
    text-align: center;
    margin-top: 20px;

    .ivu-btn {
        margin-left: 8px;
    }
}

.image-slot {
    display: flex;
    justify-content: center;
    align-items: center;
    width: 100%;
    height: 100%;
    background: #f5f7fa;
    color: #909399;
    font-size: 14px;
}

.time {
    font-size: 14px;
    font-weight: bold;
}

.content {
    padding-left: 5px;
}

.flex-column {
    display: flex;
    flex-direction: column;
    gap: 8px;
}
</style>
