<template>
    <div style="height: calc(100vh - 100px)">
        <Breadcrumb class="bread">
            <Breadcrumb-item to="/index/">光伏运维管理</Breadcrumb-item>
            <Breadcrumb-item to="/osp/">方案库</Breadcrumb-item>
            <Breadcrumb-item>方案列表</Breadcrumb-item>
        </Breadcrumb>
        <div class="wrap">
            <div class="cus-header" ref="headerRef">
                <Form :model="formSearch" :label-width="100">
                    <!-- 基础查询条件 -->
                    <div class="form-item-grid" :class="[isExpanded ? 'is-expanded' : '']">
                        <FormItem label="方案分类">
                            <Select v-model="formSearch.solutionType" placeholder="请选择方案分类" clearable
                                @on-change="handleSolutionTypeChange">
                                <Option v-for="item in solutionTypeOptions" :key="item.value" :value="item.value">{{
                                    item.label }}</Option>
                            </Select>
                        </FormItem>
                        <FormItem label="方案类型">
                            <Select v-model="formSearch.categoryId" placeholder="请选择方案类型" clearable>
                                <Option v-for="item in categoryOptions" :key="item.id" :value="item.id">{{ item.name }}
                                </Option>
                            </Select>
                        </FormItem>
                        <FormItem label="方案名称">
                            <Input v-model="formSearch.name" placeholder="输入方案名称" clearable />
                        </FormItem>

                        <FormItem label="创建人">
                            <Input v-model="formSearch.createdBy" placeholder="输入创建人" clearable />
                        </FormItem>

                        <!-- 查询按钮组 -->
                        <div class="search-buttons">
                            <Button type="default" @click="onReset">重置</Button>
                            <Button type="primary" @click="queryList" style="margin-left: 8px;">查询</Button>
                            <Button type="text" @click="toggleExpand" style="margin-left: 10px;">
                                {{ isExpanded ? '收起' : '展开' }}
                                <Icon :type="isExpanded ? 'ios-arrow-up' : 'ios-arrow-down'" />
                            </Button>
                        </div>
                    </div>
                </Form>
            </div>
            <div class="cus-main" ref="mainRef">
                <div class="cus-list" ref="cusListRef">
                    <Table :data="listArr" :columns="tableColumns" :loading="loading" class="cus-table" ref="tableRef">
                        <template slot="name" slot-scope="{ row }">
                            <span>{{ row.name || '-' }}</span>
                        </template>
                        <template slot="categoryName" slot-scope="{ row }">
                            <span>{{ row.categoryName || '-' }}</span>
                        </template>
                        <template slot="description" slot-scope="{ row }">
                            <span>{{ row.description || '-' }}</span>
                        </template>
                        <template slot="createdBy" slot-scope="{ row }">
                            <span>{{ row.createdBy || '-' }}</span>
                        </template>
                        <template slot="createdAt" slot-scope="{ row }">
                            <span>{{ row.createdAt && row.createdAt.replace('T', ' ') || '-' }}</span>
                        </template>
                        <template slot="updatedBy" slot-scope="{ row }">
                            <span>{{ row.updatedBy || '-' }}</span>
                        </template>
                        <template slot="updatedAt" slot-scope="{ row }">
                            <span>{{ row.updatedAt && row.updatedAt.replace('T', ' ') || '-' }}</span>
                        </template>
                        <template slot="action" slot-scope="{ row }">
                            <div class="center">
                                <a type="text" size="small" @click="goView(row)">预览</a>
                            </div>
                        </template>
                    </Table>
                    <Page class="cus-pages" :page-size-opts="[10, 20, 30]" :page-size="pagination.pageSize"
                        :current="pagination.pageNum" :total="total" @on-page-size-change="changeSize"
                        @on-change="changeCurrent" show-total show-sizer show-elevator ref="pageRef" />
                </div>
            </div>
        </div>
    </div>
</template>

<script>
import API from '@/api/maintainance';
import _ from 'lodash';
import _D from '@/views/osp/_edata';
import { mapActions, mapGetters } from 'vuex';

export default {
    name: 'MaintainanceSolution',
    data() {
        return {
            isExpanded: false,
            createTime: [],
            formSearch: {
                name: '',
                createdBy: '',
                solutionType: null,
                categoryId: null,
            },
            loading: false,
            listArr: [],
            total: 0,
            categoryOptions: [],
            pagination: {
                pageSize: 10,
                pageNum: 1,
            },
            tableColumns: [
                { type: 'index', title: '序号', width: 80, align: 'center', fixed: 'left' },
                { title: '方案名称', key: 'name', width: 180, align: 'center', slot: 'name', fixed: 'left' },
                { title: '所属分类', key: 'categoryName', width: 150, align: 'center', slot: 'categoryName' },
                { title: '描述', key: 'description', minWidth: 200, align: 'center', slot: 'description' },
                { title: '创建人', key: 'createdBy', width: 120, align: 'center', slot: 'createdBy' },
                { title: '创建时间', key: 'createdAt', width: 180, align: 'center', slot: 'createdAt' },
                { title: '修改人', key: 'updatedBy', width: 120, align: 'center', slot: 'updatedBy' },
                { title: '修改时间', key: 'updatedAt', width: 180, align: 'center', slot: 'updatedAt' },
                { title: '操作', key: 'action', width: 100, align: 'center', fixed: 'right', slot: 'action' }
            ]
        };
    },
    computed: {
        ...mapGetters('dict/maintainance', [
            'getDictByType',
            'getDictMapByType'
        ]),
        solutionTypeOptions() {
            return this.getDictByType ? this.getDictByType('solution_type') : [];
        },
    },
    methods: {
        ...mapActions('dict/maintainance', {
            fetchMaintainanceDict: 'fetchDict'
        }),
        async initDicts() {
            await this.fetchMaintainanceDict('solution_type');
        },
        async handleSolutionTypeChange(value) {
            this.formSearch.categoryId = null;
            this.categoryOptions = [];
            if (value) {
                try {
                    const response = await API.getSolutionCategoryList();
                    if (response.data?.result) {
                        this.categoryOptions = response.data.result.filter(item => item.solutionType === value);
                    }
                } catch (error) {
                    console.error('获取方案类型列表失败:', error);
                    this.$Message.error('获取方案类型列表失败');
                }
            }
        },
        getDictLabel(dictType, value) {
            const dictMap = this.getDictMapByType(dictType);
            return dictMap ? (dictMap[value] || '-') : '-';
        },
        async getList() {
            this.loading = true;
            try {
                const params = {
                    ...this.formSearch,
                    pageSize: this.pagination.pageSize,
                    pageNum: this.pagination.pageNum,
                };
                const response = await API.getSolutionPage(params);
                const result = response.data?.result;
                if (result) {
                    this.listArr = result.content || [];
                    this.total = result.totalElements || 0;
                } else {
                    this.listArr = [];
                    this.total = 0;
                }
            } catch (error) {
                console.error("Error fetching list:", error);
                this.listArr = [];
                this.total = 0;
                this.$Message.error('获取列表失败，请稍后再试'); // iView Message
            } finally {
                this.loading = false;
            }
        },
        queryList() {
            this.pagination.pageNum = 1;
            this.getList();
        },
        changeSize(size) {
            this.pagination.pageSize = size;
            this.getList();
        },
        changeCurrent(current) {
            this.pagination.pageNum = current;
            this.getList();
        },
        onReset: _.throttle(
            function () {
                this.createTime = [];
                Object.assign(this.formSearch, {
                    name: '',
                    createdBy: '',
                    solutionType: null, // 确保初始状态包含所有可能的查询字段
                    categoryId: null,
                });
                this.queryList();
            },
            3000,
            {
                trailing: false
            }
        ),
        toggleExpand() {
            this.isExpanded = !this.isExpanded;
        },
        goView(row) {
            this.$router.push({
                path: '/maintainance/solution/view',
                query: {
                    id: row.id
                }
            });
        }
    },
    mounted() {
        this.initDicts();
        this.getList();
    },
};
</script>

<style lang="scss" scoped>
@import "@/style/_cus_list.scss";

:deep(.ivu-tabs-bar) {
    background-color: white;
    margin-bottom: 12px;
}

:deep(.ivu-tabs-ink-bar.ivu-tabs-ink-bar-animated) {
    width: 74px !important;
}

.wrap {
    height: calc(100% - 52.38px);
    display: flex;
    gap: 12px;
    padding: 0px 12px;
    flex-direction: column;
    overflow: hidden;
    background-color: white;
}

.cus-header {
    margin-bottom: 0px;

    .form-item-grid {
        display: grid;
        grid-template-columns: repeat(3, 1fr);
        gap: 12px;
        width: 100%;

        // Adjust for iView FormItem if necessary
        .ivu-form-item:nth-child(n+3):not(:last-child) {
            display: none;
        }

        &.is-expanded {
            .ivu-form-item:nth-child(n+3):not(:last-child) {
                display: flex; // or block, depending on iView FormItem display
            }
        }
    }

    .search-buttons {
        display: flex;
        align-items: center;
        justify-content: flex-end;
        grid-column: -2 / -1;

        .ivu-btn-text {
            box-shadow: none !important;
            border: none !important;
        }
    }

    // Adjust for iView FormItem if necessary
    .ivu-form-item {
        display: flex;
        width: 100%;
        margin-bottom: 0px; // iView FormItem might have default margin

        .ivu-input-wrapper,
        // For Input
        .ivu-select,
        // For Select
        .ivu-date-picker {
            // For DatePicker
            width: 100%;
        }

        :deep(.ivu-form-item-content) {
            width: calc(100% - 100px) !important;
            margin-left: 0px !important;
        }
    }
}

.cus-main {
    display: flex;
    flex-direction: column;
    flex: 1;
    overflow: hidden;

    // >.ivu-btn {
    //     margin-bottom: 10px;
    // }

    .cus-list {
        display: flex;
        flex-direction: column;
        flex: 1;
        overflow: hidden;
        position: relative;

        .cus-table {
            flex: 1; // Table itself should take up the height calculated for it
            height: 0;
            overflow: auto;
            width: 100%;

            :deep(.ivu-table-fixed-body) {
                background-color: white;
                height: calc(100% - 56px);
            }

            :deep(.ivu-table-body) {
                height: calc(100% - 40px);
            }

            :deep(.ivu-table-tip) {
                height: calc(100% - 40px);
            }

            .center {
                display: flex;
                align-items: center;
                justify-content: center;
                gap: 12px;
            }
        }

        .cus-pages {
            margin-top: 10px;
            text-align: right;
        }
    }
}
</style>
