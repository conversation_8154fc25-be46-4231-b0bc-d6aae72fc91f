<template>
    <div class="fault-detail-page">
        <Button type="text" icon="md-undo" @click="$router.go(-1)" class="cus-back">返回</Button>

        <Card class="section-card" :bordered="false" dis-hover>
            <p slot="title" class="card-header">方案详情</p>
            <Descriptions :column="2" bordered>
                <DescriptionsItem label="方案名称">{{ formData.name }}</DescriptionsItem>
                <DescriptionsItem label="方案类型">{{ getDictLabel('solution_type', formData.solutionType) }}</DescriptionsItem>
                <DescriptionsItem label="方案品类" :span="2">{{ formData.categoryName || '-' }}</DescriptionsItem>
                <DescriptionsItem label="方案描述" :span="2">
                    <div v-text="formData.description || '-'"></div>
                </DescriptionsItem>
                <DescriptionsItem label="方案文件" :span="2">
                    <div v-if="formData.file" class="file-item">
                        <a :href="formData.file" target="_blank" rel="noopener noreferrer">
                            <Icon type="md-document" size="16" style="margin-right: 5px;" />
                            {{ `${formData.name}.pdf` }}
                        </a>
                    </div>
                    <span v-else>-</span>
                </DescriptionsItem>
                <DescriptionsItem label="方案视频" :span="2">
                     <div v-if="formData.video" class="file-item">
                        <a :href="formData.video" target="_blank" rel="noopener noreferrer">
                            <Icon type="md-videocam" size="16" style="margin-right: 5px;" />
                            {{ `${formData.name}.mp4` }}
                        </a>
                    </div>
                    <span v-else>-</span>
                </DescriptionsItem>
                <DescriptionsItem label="创建人">{{ formData.createdBy || '-' }}</DescriptionsItem>
                <DescriptionsItem label="创建时间">{{ formData.createdAt || '-' }}</DescriptionsItem>
                <DescriptionsItem label="最后修改人">{{ formData.updatedBy || '-' }}</DescriptionsItem>
                <DescriptionsItem label="最后修改时间">{{ formData.updatedAt || '-' }}</DescriptionsItem>
            </Descriptions>
        </Card>
    </div>
</template>

<script>
import API from '@/api/maintainance';
import { mapActions, mapGetters } from 'vuex';
import Descriptions from '../components/Descriptions';
import DescriptionsItem from '../components/DescriptionItem';

export default {
    name: 'FaultDetail',
    components: {
        Descriptions,
        DescriptionsItem
    },
    data() {
        return {
            formData: {
                name: '',
                categoryName: '',
                solutionType: '',
                description: '',
                file: '',
                video: '',
                createdBy: '',
                createdAt: '',
                updatedBy: '',
                updatedAt: '',
            },
        };
    },
    computed: {
        ...mapGetters('dict/maintainance', [
            'getDictByType',
            'getDictMapByType'
        ]),
        editable() {
            // 保持原有逻辑，如果不需要可以移除
            return ['TO_PROCESS'].includes(this.formData.orderStatus) && this.action !== 'view';
        },
        getFileName() {
            return (url) => {
                if (!url) return '-';
                try {
                    // 先尝试从URL路径中获取文件名
                    const pathname = new URL(url).pathname;
                    const lastSlashIndex = pathname.lastIndexOf('/');
                    if (lastSlashIndex !== -1 && lastSlashIndex < pathname.length -1) {
                        return decodeURIComponent(pathname.substring(lastSlashIndex + 1));
                    }
                    // 如果路径中没有文件名，尝试从查询参数中获取（例如 content-disposition 提示的文件名）
                    // 这是一个简化的例子，实际可能需要更复杂的解析
                    const params = new URL(url).searchParams;
                    if (params.has('filename')) {
                        return params.get('filename');
                    }

                } catch (e) {
                    // 如果URL解析失败，或者无法有效提取，返回整个URL或一部分作为备用
                    const parts = url.split('/');
                    return parts.length > 0 ? parts[parts.length - 1] : url;
                }
                // 默认返回URL的最后一部分
                const parts = url.split('/');
                return parts.length > 0 ? decodeURIComponent(parts[parts.length - 1]) : '未知文件';
            };
        }
    },
    methods: {
        ...mapActions('dict/maintainance', {
            fetchMaintainanceDict: 'fetchDict'
        }),
        getDictLabel(dictType, value) {
            const dictMap = this.getDictMapByType(dictType);
            return dictMap ? (dictMap[value] || value || '-') : (value || '-');
        },
        async initDicts() {
            await this.fetchMaintainanceDict('solution_type');
        },
        async getDetail() {
            const id = this.$route.query.id;
            if (!id) {
                this.$Message.error('缺少id');
                this.$router.back();
                return;
            }
            // this.formData.id = id; // formData中已有id，无需重复赋值

            try {
                const res = (await API.getSolution({ id })).data; // API.getSolution 定义在 maintainance.js
                if (res.success && res.result) {
                    // 使用提供的JSON数据结构进行赋值
                    this.formData = {
                        id: res.result.id,
                        name: res.result.name,
                        categoryId: res.result.categoryId,
                        categoryName: res.result.categoryName,
                        solutionType: res.result.solutionType,
                        description: res.result.description,
                        file: res.result.file,
                        video: res.result.video,
                        createdBy: res.result.createdBy,
                        updatedBy: res.result.updatedBy,
                        createdAt: res.result.createdAt,
                        updatedAt: res.result.updatedAt,
                        delFlag: res.result.delFlag
                        // 如果还有其他字段，也在这里添加
                    };
                } else {
                    this.$Message.error(res.message || res.error || '获取详情失败');
                }
            } catch (error) {
                console.error('获取详情失败:', error);
                this.$Message.error('获取详情数据时发生错误，请查看控制台');
            }
        },
    },
    created() {
        this.getDetail();
        this.initDicts();
    }
};
</script>

<style scoped lang="scss">
.fault-detail-page {
    background-color: white;
    padding: 16px;
}

.cus-back {
    margin-bottom: 16px;
}

.section-card {
    margin-bottom: 16px;
    ::v-deep .ivu-card-head { // 确保iview组件的样式能被覆盖
        padding: 10px 16px;
    }
}

.card-header {
    font-weight: bold;
    font-size: 16px; // 调整标题字号
}

.action-buttons {
    text-align: center;
    margin-top: 20px;

    .ivu-btn {
        margin-left: 8px;
    }
}

.file-item {
    display: flex;
    align-items: center;
    a {
        color: #2d8cf0; // View UI 默认链接颜色
        text-decoration: none;
        &:hover {
            text-decoration: underline;
        }
    }
}

// Descriptions 组件的样式调整 (如果需要)
::v-deep .ivu-descriptions-item-label {
    font-weight: bold;
}
</style>
