<template>
	<div class="station-detail-page">
		<Button type="text" icon="md-undo" @click="$router.go(-1)">返回</Button>

		<!-- 电站信息 -->
		<Card class="section-card">
			<p slot="title">电站信息</p>
			<Descriptions :column="2" bordered>
				<DescriptionItem label="电站编码">{{ formData.stationCode || '-' }}</DescriptionItem>
				<DescriptionItem label="逆变器SN码">
					<Button v-if="formData.inverterSn" type="text">
						{{ formData.inverterSn }}
					</Button>
					<span v-else>-</span>
				</DescriptionItem>
				<DescriptionItem label="业主姓名">{{ formData.name || '-' }}</DescriptionItem>
				<DescriptionItem label="手机号">{{ formData.phone || '-' }}</DescriptionItem>
				<DescriptionItem label="电站模式">{{ detailMode[formData.mode] || '-' }}</DescriptionItem>
				<DescriptionItem label="关联资方">{{ pmSpecialFlag[formData.specialFlag] || '-' }}</DescriptionItem>
				<DescriptionItem label="所属分中心">{{ formData.subCenterName || '-' }}</DescriptionItem>
				<DescriptionItem label="服务商类别">{{ identityType[formData.opType] || '-' }}</DescriptionItem>
				<DescriptionItem label="运维商名称">{{ formData.opName || '-' }}</DescriptionItem>
				<DescriptionItem label="运维商业务类型">{{ businessType[formData.businessType] || '-' }}</DescriptionItem>
				<DescriptionItem label="是否质保期内">{{ isWarranty[formData.isWarranty] || '-' }}</DescriptionItem>
				<DescriptionItem label="区域">{{ (formData.provinceName || '') + (formData.cityName || '') +
					(formData.regionName
						|| '') || '-' }}</DescriptionItem>
				<DescriptionItem label="详细地址">{{ formData.address || '-' }}</DescriptionItem>
			</Descriptions>
		</Card>

    <!-- 逆变器信息 -->
		<Card v-for="item in inverterList" :key="item.inverterSn" class="section-card">
			<p slot="title">逆变器{{ item.inverterSn }}</p>
			<Descriptions :column="2" bordered>
				<DescriptionItem label="逆变器SN码">
					{{ item.inverterSn || '-' }}
				</DescriptionItem>
				<DescriptionItem label="逆变器品牌">
					{{ item.brandName || '-' }}
				</DescriptionItem>
				<DescriptionItem label="逆变器型号">
          {{ item.inverterModel || '-'  }}
				</DescriptionItem>
				<DescriptionItem label="逆变器图片">
					<template v-if="item.imageUrl">
            <img
              style="width: 100px; height: 100px; cursor: pointer; object-fit: cover;"
              :src="item.imageUrl"
              @click="handlePreview(item.imageUrl)" />
          </template>
          <span v-else>-</span>
				</DescriptionItem>
        <DescriptionItem label="装机容量">
          {{ item.power || '-'  }}
				</DescriptionItem>
        <DescriptionItem label="实时功率">
          {{ item.pac || '-'  }}
				</DescriptionItem>
        <DescriptionItem label="日发电量(kWh)">
          {{ item.elecDay || '-'  }}
				</DescriptionItem>
        <DescriptionItem label="月发电量(kWh)">
          {{ item.elecMonth || '-'  }}
				</DescriptionItem>
        <DescriptionItem label="年发电量(kWh)">
          {{ item.elecYear || '-'  }}
				</DescriptionItem>
        <DescriptionItem label="累计发电量(kWh)">
          {{ item.elecTotal || '-'  }}
				</DescriptionItem>
        <DescriptionItem label="日等效小时数">
          {{ item.dailyEquivalentHours || '-'  }}
				</DescriptionItem>
        <DescriptionItem label="逆变器状态">
          {{ item.inveterState || '-'  }}
				</DescriptionItem>
        <DescriptionItem label="组串数量">
          {{ item.stringDetailParsed ? item.stringDetailParsed.length : '0'  }}
				</DescriptionItem>
        <DescriptionItem label="组串总块数">
          {{ getTotalPvCount(item.stringDetailParsed) }}
				</DescriptionItem>
        <DescriptionItem label="组串详情" :span="2">
          <div class="string-detail-container">
            <div v-if="item.stringDetailParsed && item.stringDetailParsed.length > 0" class="string-detail-content">
              <div v-for="mppt in item.stringDetailParsed" :key="mppt.name" class="mppt-item">
                <div class="mppt-header">
                  <Tag color="primary" class="mppt-tag">{{ mppt.name }}</Tag>
                  <span class="mppt-total">总数: {{ mppt.total || 0 }}</span>
                </div>
                <div v-if="mppt.pv && mppt.pv.length > 0" class="pv-list">
                  <Tag
                    v-for="pv in mppt.pv"
                    :key="pv.name"
                    class="pv-tag"
                  >
                    {{ pv.name }}: {{ pv.total || 0 }}
                  </Tag>
                </div>
              </div>
            </div>
            <Button type="text" class="edit-button" @click="onEditStringDetail(item)">
              <Icon type="md-create" />
              {{ item.stringDetailParsed && item.stringDetailParsed.length > 0 ? '编辑' : '新增' }}
            </Button>
          </div>
				</DescriptionItem>
			</Descriptions>
      <Tabs v-model="item.activeTab" type="card" class="section-card" style="margin-top: 20px" @on-click="onTabChange(item)">
        <TabPane label="功率/温度" name="power-temp">
          <DatePicker
            v-model="item.powerTempQueryDate"
            type="date"
            placeholder="选择日期"
            format="yyyy-MM-dd"
            :options="datePickerOptions"
            @on-change="onDateChange(item, 'power-temp')"
            style="width: 150px; margin-bottom: 20px;"
          />
          <div :id="'powerTempChart-' + item.inverterSn" style="width: 100%; height: 400px"></div>
        </TabPane>
        <TabPane label="MPPT" name="mppt">
          <DatePicker
            v-model="item.mpptQueryDate"
            type="date"
            placeholder="选择日期"
            format="yyyy-MM-dd"
            :options="datePickerOptions"
            @on-change="onDateChange(item, 'mppt')"
            style="width: 150px; margin-bottom: 20px;"
          />
          <div :id="'mpptChart-' + item.inverterSn" style="width: 100%; height: 400px"></div>
        </TabPane>
      </Tabs>
		</Card>
    <WorkOrderList v-if="formData.stationCode" :stationCode="formData.stationCode" />
    <StringDetailEditDialog
      v-if="editingInverter"
      :visible.sync="isDialogVisible"
      :data="editingInverter ? editingInverter.stringDetailParsed || [] : []"
      @submit="handleStringDetailUpdate"
      ref="dialogRef"
    />

    <!-- 图片预览 -->
    <Modal title="图片预览" v-model="previewImageVisible">
      <img :src="previewImageUrl" v-if="previewImageVisible" style="width: 100%">
    </Modal>
	</div>
</template>

<script>
import * as echarts from 'echarts';
import API from '@/api/maintainance'
import _D from '@/views/osp/_edata';
import _ from 'lodash';
import { mapActions, mapGetters } from 'vuex';
import WorkOrderList from './components/WorkOrderList.vue';
import StringDetailEditDialog from './components/StringDetailEditDialog.vue';
import Descriptions from '../components/Descriptions.vue';
import DescriptionItem from '../components/DescriptionItem.vue';

export default {
  name: 'StationDetail',
  components: {
    WorkOrderList,
    StringDetailEditDialog,
    Descriptions,
    DescriptionItem
  },
  data() {
    return {
      pmSpecialFlag: _D.pmSpecialFlag,
      businessType: _D.businessType,
      isWarranty: _D.isWarranty,
      identityType: _D.identityType,
      detailMode: _D.detailMode,
      inverterList: [],
      chartInstances: [],
      isDialogVisible: false,
      editingInverter: null,
      previewImageVisible: false,
      previewImageUrl: '',
      datePickerOptions: {
        disabledDate: (date) => {
          const today = new Date();
          today.setHours(23, 59, 59, 999);
          return date.getTime() > today.getTime();
        }
      },
      formData: {
        address: '',
        auditMode: '',
        auditUnit: '',
        businessType: '',
        cityId: '',
        cityName: '',
        closeReason: '',
        closeTime: '',
        configCheckItems: [],
        configId: '',
        createdAt: '',
        createdBy: '',
        deadline: '',
        devices: {},
        dispatchAuditPermission: '',
        dispatchMode: '',
        dispatchUnit: '',
        dispatched: null,
        faultDescription: '',
        faultInfos: [],
        finishFlag: '',
        finishTime: '',
        firstAuditResult: '',
        handleCheckItems: [],
        handleTime: '',
        handler: '',
        id: '',
        inverterSn: '',
        isWarranty: null,
        opCode: '',
        opMemberId: '',
        opName: '',
        opType: '',
        orderCode: '',
        orderName: '',
        orderSource: '',
        orderStatus: '',
        orderType: '',
        overTime: null,
        processes: [],
        provinceId: '',
        provinceName: '',
        regionId: '',
        regionName: '',
        remark: '',
        secondAuditResult: '',
        sparePartApplyNo: '',
        sparePartAuditPassTime: '',
        sparePartAuditStatus: '',
        specialFlag: '',
        stationCode: '',
        stationMode: '',
        stationName: '',
        stationPhone: '',
        streetId: '',
        streetName: '',
        subCenterName: '',
        name: '',
        phone: '',
        mode: ''
      }
    };
  },
  computed: {
    ...mapGetters('dict/maintainance', [
      'getDictByType',
      'getDictMapByType'
    ])
  },
  methods: {
    ...mapActions('dict/maintainance', {
      fetchMaintainanceDict: 'fetchDict'
    }),

    getTotalPvCount(parsedDetails) {
      if (!parsedDetails || !Array.isArray(parsedDetails)) return 0;
      return parsedDetails.reduce((sum, mppt) => {
        if (!mppt.pv || !Array.isArray(mppt.pv)) return sum;
        const mpptTotal = mppt.pv.reduce((pvSum, pv) => pvSum + (pv.total || 0), 0);
        return sum + mpptTotal;
      }, 0);
    },

    handlePreview(url) {
      this.previewImageUrl = url;
      this.previewImageVisible = true;
    },

    async onEditStringDetail(inverter) {
      console.log('onEditStringDetail: 打开编辑对话框', {
        inverterSn: inverter.inverterSn,
        stringDetailParsed: inverter.stringDetailParsed,
        originalStringDetailParsed: inverter.originalStringDetailParsed
      });

      // 先设置编辑的逆变器
      this.editingInverter = inverter;

      // 等待下一个 tick 确保数据设置完成
      await this.$nextTick();

      // 再显示对话框
      this.isDialogVisible = true;
    },

    async handleStringDetailUpdate(updatedData) {
      if (!this.editingInverter) return;

      const inverter = this.editingInverter;
      const stringDetail = JSON.stringify(updatedData);

      try {
        const payload = {
          inverterSn: inverter.inverterSn,
          stringDetail: stringDetail,
        };
        // TODO: 待后端提供接口, 例如: API.updateStationInverterStringDetail(payload)
        // const res = await API.updateStationInverterStringDetail(payload);

        // 模拟API调用
        await new Promise(resolve => setTimeout(resolve, 500));
        const res = { success: true };

        if (res.success) {
          this.$Message.success('更新成功');
          console.log('handleStringDetailUpdate: 保存成功，更新数据', {
            inverterSn: inverter.inverterSn,
            updatedData: updatedData,
            beforeUpdate: {
              stringDetailParsed: inverter.stringDetailParsed,
              originalStringDetailParsed: inverter.originalStringDetailParsed
            }
          });

          // 更新数据
          inverter.stringDetailParsed = _.cloneDeep(updatedData);
          inverter.originalStringDetailParsed = _.cloneDeep(updatedData);

          console.log('handleStringDetailUpdate: 数据更新完成', {
            afterUpdate: {
              stringDetailParsed: inverter.stringDetailParsed,
              originalStringDetailParsed: inverter.originalStringDetailParsed
            }
          });

          // 延迟关闭对话框，确保数据更新完成
          await this.$nextTick();
          this.isDialogVisible = false;
          this.editingInverter = null;
        } else {
          this.$Message.error(res.error || '更新失败');
        }
      } catch (error) {
        console.error('更新组串详情失败:', error);
        this.$Message.error('更新组串详情时发生错误');
      } finally {
        this.$refs.dialogRef?.resetSubmitting();
      }
    },

    initChart(elementId, title, seriesData, yAxis, xAxisData) {
      const chartDom = document.getElementById(elementId);
      if (!chartDom) return;

      let myChart = echarts.getInstanceByDom(chartDom);
      if (!myChart) {
        myChart = echarts.init(chartDom);
        this.chartInstances.push(myChart);
      }

      const option = {
        title: {
          text: title,
          left: 'center'
        },
        tooltip: {
          trigger: 'axis',
          axisPointer: {
            type: 'cross'
          }
        },
        legend: {
          data: seriesData.map(s => s.name),
          top: 30
        },
        grid: {
          right: '10%'
        },
        xAxis: {
          type: 'category',
          data: xAxisData,
          axisPointer: {
            type: 'shadow'
          }
        },
        yAxis: yAxis,
        dataZoom: [
          {
            type: 'slider',
            start: 0,
            end: 100,
          },
        ],
        series: seriesData
      };
      myChart.setOption(option, true);
    },

    onTabChange(inverter) {
      this.$nextTick(() => {
        if (inverter.activeTab === 'power-temp') {
          const xAxisData = inverter.inverterData.map(item => item.dataTimestamp);
          const powerData = inverter.inverterData.map(item => item.pac);
          const tempData = inverter.inverterData.map(item => item.inverterTemperature);
          this.initChart(
            'powerTempChart-' + inverter.inverterSn,
            '功率/温度曲线',
            [{
              name: '功率',
              type: 'line',
              smooth: true,
              data: powerData,
              yAxisIndex: 0,
            },
            {
              name: '温度',
              type: 'line',
              smooth: true,
              data: tempData,
              yAxisIndex: 1,
            }],
            [
              {
                type: 'value',
                name: '功率(W)',
                position: 'left',
                axisLine: {
                  show: true,
                },
              },
              {
                type: 'value',
                name: '温度(℃)',
                position: 'right',
                axisLine: {
                  show: true,
                },
              }
            ],
            xAxisData
          );
        } else if (inverter.activeTab === 'mppt') {
          const series = [];
          let xAxisData = [];
          if (inverter.mpptData && Object.keys(inverter.mpptData).length > 0) {
            const mpptNames = Object.keys(inverter.mpptData);

            mpptNames.forEach(mpptName => {
              const mpptGroupData = inverter.mpptData[mpptName];
              if (mpptGroupData.length > 0) {
                series.push({
                  name: mpptName,
                  type: 'line',
                  smooth: true,
                  data: mpptGroupData.map(item => item.power)
                });
              }
            });

            if (series.length > 0) {
              const firstMpptGroup = inverter.mpptData[mpptNames[0]];
              if (firstMpptGroup) {
                xAxisData = firstMpptGroup.map(item => item.dataTimestamp);
              }
            }
          }
          this.initChart(
            `mpptChart-${inverter.inverterSn}`,
            'MPPT 功率曲线',
            series,
            {
              type: 'value',
              name: '功率(W)'
            },
            xAxisData
          );
        }
      });
    },

    async loadPowerTempChartData(inverter) {
      const params = {
        inverterSn: inverter.inverterSn,
        date: inverter.powerTempQueryDate,
      };
      const inverterDataRes = await API.getStationInverterData(params);
      inverter.inverterData = inverterDataRes.success ? inverterDataRes.result : [];
    },

    async loadMpptChartData(inverter) {
      const params = {
        inverterSn: inverter.inverterSn,
        date: inverter.mpptQueryDate,
      };
      const mpptDataRes = await API.getStationInverterMpptData(params);
      const rawMpptData = mpptDataRes.success ? mpptDataRes.result : [];
      if (rawMpptData.length > 0) {
        const grouped = _.groupBy(rawMpptData, 'mpptName');
        for (const mpptName in grouped) {
          grouped[mpptName].sort((a, b) => new Date(a.dataTimestamp) - new Date(b.dataTimestamp));
        }
        inverter.mpptData = grouped;
      } else {
        inverter.mpptData = {};
      }
    },

    async onDateChange(item, chartType) {
      if (chartType === 'power-temp') {
        await this.loadPowerTempChartData(item);
      } else if (chartType === 'mppt') {
        await this.loadMpptChartData(item);
      }
      this.onTabChange(item);
    },

    async getDetail() {
      const stationCode = this.$route.query.stationCode
      if (!stationCode) {
        this.$Message.error('缺少工单标识')
        this.$router.back()
        return
      }
      this.formData.stationCode = stationCode

      try {
        const res = await API.getStationByStationCode({ stationCode })

        if (res.success) {
          Object.assign(this.formData, res.result)
        } else {
          this.$Message.error(res.error || '获取详情失败')
        }

        const inverterListRes = await API.getStationInverterList({ stationCode });
        if (inverterListRes.success && inverterListRes.result) {
          const today = new Date().toISOString().slice(0, 10);
          const inverters = inverterListRes.result.map(inv => {
            let stringDetailParsed, originalStringDetailParsed;
            try {
              // 确保stringDetail存在且为有效JSON，否则使用空数组
              const details = JSON.parse(inv.stringDetail || '[]');
              stringDetailParsed = Array.isArray(details) ? details : [];
            } catch (e) {
              console.error('解析组串详情失败:', e);
              stringDetailParsed = [];
            }
            originalStringDetailParsed = _.cloneDeep(stringDetailParsed);

            return {
              ...inv,
              activeTab: 'power-temp',
              powerTempQueryDate: today,
              mpptQueryDate: today,
              inverterData: [],
              mpptData: {},
              stringDetailParsed,
              originalStringDetailParsed
            };
          });

          this.inverterList = inverters;

          for (const inverter of this.inverterList) {
            await Promise.all([this.loadPowerTempChartData(inverter), this.loadMpptChartData(inverter)]);
            this.onTabChange(inverter);
            const elecDataRes = await API.getStationInverterElecData({ inverterSn: inverter.inverterSn });
            inverter.elecData = elecDataRes.success ? elecDataRes.result : null;
          }
        }
      } catch (error) {
        console.error('获取详情失败:', error)
        this.$Message.error('获取详情数据时发生错误')
      }
    }
  },

  mounted() {
    this.getDetail()
    this.fetchMaintainanceDict([
      'close_order_reason',
    ]);
  },

  beforeDestroy() {
    this.chartInstances.forEach(chart => {
      chart.dispose();
    });
    this.chartInstances = [];
  }
};

</script>

<style lang="scss" scoped>
@import '@/style/_variables.scss';

.station-detail-page {
  padding: 20px;
  background-color: #f8f9fa;
  height: 100%;
  box-sizing: border-box;
  overflow: auto;

  .section-card {
    margin-bottom: 20px;
    border-radius: 4px;
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.05);

    .card-header {
      display: flex;
      align-items: center;
      font-size: 16px;
      font-weight: 600;
      color: #303133;
    }

    // iView Card 样式调整
    ::v-deep .ivu-card-head {
      background-color: #ffffff;
      padding: 12px 20px;
      border-bottom: 1px solid $border-color-1;
    }

    ::v-deep .ivu-card-body {
      padding: 20px;
    }
  }

  .info-label {
    color: #6c757d;
    margin-bottom: 8px;
    font-size: 14px;
  }

  .image-slot {
    display: flex;
    justify-content: center;
    align-items: center;
    width: 100%;
    height: 100%;
    background: #f5f7fa;
    color: #c0c4cc;
    font-size: 14px;
  }

  // 组串详情样式
  .string-detail-container {
    display: flex;
    align-items: flex-start;
    gap: 16px;

    .string-detail-content {
      flex: 1;
    }

    .mppt-item {
      margin-bottom: 12px;
      padding: 12px;
      background: #f8f9fa;
      border-radius: 6px;
      border-left: 3px solid $color-main;

      &:last-child {
        margin-bottom: 0;
      }
    }

    .mppt-header {
      display: flex;
      align-items: center;
      gap: 8px;
      margin-bottom: 8px;

      .mppt-tag {
        font-weight: 600;
      }

      .mppt-total {
        color: #606266;
        font-size: 13px;
      }
    }

    .pv-list {
      display: flex;
      flex-wrap: wrap;
      gap: 6px;

      .pv-tag {
        background: #fff;
        border: 1px solid $border-color-1;
        color: #606266;
        font-size: 12px;
      }
    }

    .edit-button {
      display: flex;
      align-items: center;
      gap: 4px;
      padding: 8px 12px;
      border-radius: 4px;
      transition: all 0.3s;

      &:hover {
        background: #ecf5ff;
      }
    }
  }
}
</style>
