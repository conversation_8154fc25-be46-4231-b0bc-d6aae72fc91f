<template>
  <Tabs value="workorder" type="card" class="section-card">
    <TabPane label="工单列表" name="workorder">
      <div class="wrap">
        <div class="cus-header">
          <Form :model="formSearch" :label-width="100">
            <div class="form-item-grid" :class="[isExpanded ? 'is-expanded' : '']">
              <FormItem label="运维单号">
                <Input v-model="formSearch.orderCode" placeholder="输入运维单号" clearable />
              </FormItem>
              <FormItem label="工单类型">
                <Select v-model="formSearch.orderType" placeholder="选择工单类型" clearable style="width: 100%;">
                  <Option v-for="item in workOrderTypeOptions" :key="item.value" :label="item.label"
                    :value="item.value" />
                </Select>
              </FormItem>
              <FormItem label="工单来源">
                <Select v-model="formSearch.orderSource" placeholder="选择工单来源" clearable style="width: 100%;">
                  <Option v-for="item in sourceOptions" :key="item.value" :label="item.label"
                    :value="item.value" />
                </Select>
              </FormItem>
              <FormItem label="生成工单时间">
                <DatePicker v-model="createTime" type="daterange" format="yyyy-MM-dd"
                  start-placeholder="开始时间" end-placeholder="结束时间" @on-change="dateChange" clearable style="width: 100%;" />
              </FormItem>
              <div class="search-buttons">
                <Button @click="onReset">重置</Button>
                <Button type="primary" @click="queryList">查询</Button>
                <Button type="text" @click="toggleExpand" style="margin-left: 10px;">
                  {{ isExpanded ? '收起' : '展开' }}
                  <Icon :type="isExpanded ? 'ios-arrow-up' : 'ios-arrow-down'" />
                </Button>
              </div>
            </div>
          </Form>
        </div>
        <div class="cus-main">
          <div class="cus-list" v-loading="loading">
            <Table :data="listArr" class="cus-table">
              <Column type="index" align="center" title="序号" width="60" />
              <Column prop="orderCode" align="center" title="运维单号" width="200" />
              <Column prop="stationCode" align="center" title="电站编码" width="200">
                <template slot-scope="{ row }">
                  {{ row.stationCode || '-' }}
                </template>
              </Column>
              <Column prop="inverterSn" align="center" title="逆变器SN码" width="150">
                <template slot-scope="{ row }">
                  {{ row.inverterSn || '-' }}
                </template>
              </Column>
              <Column prop="orderSource" align="center" title="工单来源" width="120">
                <template slot-scope="{ row }">
                  {{ getDictLabel('work_order_source', row.orderSource) }}
                </template>
              </Column>
              <Column prop="orderType" align="center" title="工单类型" width="120">
                <template slot-scope="{ row }">
                  {{ getDictLabel('work_order_type', row.orderType) }}
                </template>
              </Column>
              <Column prop="orderStatus" align="center" title="工单状态" width="120">
                <template slot-scope="{ row }">
                  {{ getDictLabel('work_order_status', row.orderStatus) }}
                </template>
              </Column>
              <Column prop="faultDescription" align="center" title="故障现象" width="150" />
              <Column prop="mode" align="center" title="模式" width="120" />
              <Column prop="specialFlag" align="center" title="资产所属" width="120">
                <template slot-scope="{ row }">
                  {{ pmSpecialFlag[row.specialFlag] || '-' }}
                </template>
              </Column>
              <Column prop="stationType" align="center" title="电站类型" width="120">
                <template slot-scope="{ row }">
                  {{ detStationType[row.stationType] || '-' }}
                </template>
              </Column>
              <Column prop="projectCompanyName" align="center" title="所属项目公司" width="150" />
              <Column prop="opName" align="center" title="运维商" width="150" />
              <Column prop="subCenterName" align="center" title="所属分中心" width="120" />
              <Column prop="stationName" align="center" title="电站业主" width="120" />
              <Column prop="stationPhone" align="center" title="业主联系方式" width="150" />
              <Column prop="regionName" align="center" title="区域" width="120" />
              <Column prop="address" align="center" title="详细地址" width="200" />
              <Column prop="isWarranty" align="center" title="是否在质保期" width="120">
                <template slot-scope="{ row }">
                  {{ isWarranty[row.isWarranty] || '-' }}
                </template>
              </Column>
              <Column prop="opBusinessType" align="center" title="运维业务类型" width="150">
                <template slot-scope="{ row }">
                  {{ businessType[row.opBusinessType] || '-' }}
                </template>
              </Column>
              <Column fixed="right" align="center" title="操作" width="120">
                <template slot-scope="{ row }">
                  <Button type="text" @click="viewOrder(row)">查看</Button>
                  <Button v-if="['TO_HEAD_DISPATCH', 'TO_SUB_CENTER_DISPATCH'].includes(row.orderStatus)"
                    type="text">关单</Button>
                </template>
              </Column>
            </Table>
            <Page class="cus-pages" v-if="total"
              :page-size-opts="[10, 20, 30]" :page-size="pagination.pageSize" :current="pagination.pageNum"
              :total="total" @on-page-size-change="changeSize" @on-change="changeCurrent"
              show-sizer show-total />
          </div>
        </div>
      </div>
    </TabPane>
  </Tabs>
</template>

<script>
import API from '@/api/maintainance';
import _D from '@/edata/_osp_data';
import _ from 'lodash';
import { mapActions, mapGetters } from 'vuex';

export default {
  name: 'WorkOrderList',
  props: {
    stationCode: {
      type: String,
      required: true,
    },
  },
  data() {
    return {
      pmSpecialFlag: _D.pmSpecialFlag,
      businessType: _D.businessType,
      isWarranty: _D.isWarranty,
      detStationType: _D.detStationType,
      isExpanded: false,
      createTime: '',
      formSearch: {
        createTimeEnd: null,
        createTimeStart: null,
        orderCode: '',
        stationCode: this.stationCode,
        orderSource: '',
        orderType: '',
      },
      loading: false,
      listArr: [],
      total: 0,
      pagination: {
        pageNum: 1,
        pageSize: 10
      }
    };
  },
  computed: {
    ...mapGetters('dict/maintainance', [
      'getDictByType',
      'getDictMapByType'
    ]),
    sourceOptions() {
      return this.getDictByType('work_order_source') || [];
    },
    workOrderTypeOptions() {
      return this.getDictByType('work_order_type') || [];
    }
  },
  methods: {
    ...mapActions('dict/maintainance', {
      fetchMaintainanceDict: 'fetchDict'
    }),
    getDictLabel(dictType, value) {
      const dictMap = this.getDictMapByType(dictType);
      return dictMap ? (dictMap[value] || '-') : '-';
    },

    dateChange(e) {
      this.formSearch.createTimeStart = e ? e[0] : null;
      this.formSearch.createTimeEnd = e ? e[1] : null;
    },

    onReset: _.throttle(
      function() {
        this.createTime = '';
        const stationCode = this.formSearch.stationCode;
        Object.assign(this.formSearch, {
          createTimeEnd: null,
          createTimeStart: null,
          orderCode: '',
          orderSource: '',
          orderType: '',
        });
        this.formSearch.stationCode = stationCode;
        this.queryList();
      },
      3000,
      { trailing: false }
    ),

    viewOrder(row) {
      this.$router.push({
        path: '/maintainance/faultWorkOrder/detail',
        query: {
          orderCode: row.orderCode,
          action: 'view'
        }
      });
    },

    toggleExpand() {
      this.isExpanded = !this.isExpanded;
    },

    async getList() {
      this.loading = true;
      try {
        const res = await API.getWorkOrderPage({
          ...this.formSearch,
          pageNum: this.pagination.pageNum,
          pageSize: this.pagination.pageSize
        });
        if (res.success) {
          this.listArr = res.result.records || [];
          this.total = res.result.total || 0;
        }
      } catch (error) {
        console.error('获取工单列表失败:', error);
        this.$Message.error('获取工单列表失败');
      } finally {
        this.loading = false;
      }
    },

    queryList() {
      this.pagination.pageNum = 1;
      this.getList();
    },

    changeSize(pageSize) {
      this.pagination.pageSize = pageSize;
      this.pagination.pageNum = 1;
      this.getList();
    },

    changeCurrent(pageNum) {
      this.pagination.pageNum = pageNum;
      this.getList();
    }
  },

  mounted() {
    this.getList();
    this.fetchMaintainanceDict([
      'work_order_type',
      'work_order_source',
      'work_order_status',
    ]);
  }
};
</script>

<style lang="scss" scoped>
@import '@/style/_variables.scss';

.section-card {
  margin-bottom: 20px;
  border-radius: 4px;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.05);
}

.wrap {
  height: 600px;
  padding: 0px;
}

.cus-header {
  margin-bottom: 0px;
  padding: 20px;
  background: #fff;
  border-bottom: 1px solid $border-color-1;

  .form-item-grid {
    display: grid;
    grid-template-columns: repeat(4, 1fr);
    gap: 12px;
    width: 100%;

    .ivu-form-item:nth-child(n+4) {
      display: none;
    }

    &.is-expanded {
      .ivu-form-item:nth-child(n+4) {
        display: flex;
      }
    }
  }

  .search-buttons {
    display: flex;
    align-items: center;
    justify-content: flex-end;
    grid-column: -2 / -1;
  }

  .ivu-form-item {
    width: 100%;
    margin-bottom: 0px;

    .ivu-input,
    .ivu-select,
    .ivu-date-picker {
      width: 100%;
    }
  }
}

.cus-main {
  display: flex;
  flex-direction: column;
  flex: 1;
  overflow: hidden;
  margin-top: 10px;

  .cus-list {
    display: flex;
    flex-direction: column;
    flex: 1;
    overflow: hidden;
    position: relative;
    padding: 0 20px;

    .cus-table {
      flex: 1;
      overflow: auto;
      height: 100%;
    }

    .cus-pages {
      margin-top: 10px;
      text-align: right;
    }
  }
}
</style>
