<template>
  <el-tabs model-value="workorder" type="border-card" class="section-card">
    <el-tab-pane label="工单列表" name="workorder">
      <div class="wrap">
        <div class="cus-header">
          <el-form :model="formSearch" label-width="100px">
            <div class="form-item-grid" :class="[isExpanded ? 'is-expanded' : '']">
              <el-form-item label="运维单号">
                <el-input v-model="formSearch.orderCode" placeholder="输入运维单号" clearable />
              </el-form-item>
              <el-form-item label="工单类型">
                <el-select v-model="formSearch.orderType" placeholder="选择工单类型" clearable style="width: 100%;">
                  <el-option v-for="item in workOrderTypeOptions" :key="item.value" :label="item.label"
                    :value="item.value" />
                </el-select>
              </el-form-item>
              <el-form-item label="工单来源">
                <el-select v-model="formSearch.orderSource" placeholder="选择工单来源" clearable style="width: 100%;">
                  <el-option v-for="item in sourceOptions" :key="item.value" :label="item.label"
                    :value="item.value" />
                </el-select>
              </el-form-item>
              <el-form-item label="生成工单时间">
                <el-date-picker v-model="createTime" type="daterange" value-format="YYYY-MM-DD"
                  start-placeholder="开始时间" end-placeholder="结束时间" @change="dateChange" clearable style="width: 100%;" />
              </el-form-item>
              <div class="search-buttons">
                <el-button type="default" @click="onReset">重置</el-button>
                <el-button type="primary" @click="queryList">查询</el-button>
                <el-link type="primary" @click="toggleExpand" :underline="false" style="margin-left: 10px;">
                  {{ isExpanded ? '收起' : '展开' }}
                  <el-icon>
                    <arrow-up v-if="isExpanded" />
                    <arrow-down v-else />
                  </el-icon>
                </el-link>
              </div>
            </div>
          </el-form>
        </div>
        <div class="cus-main">
          <div class="cus-list" v-loading="loading">
            <el-table :data="listArr" class="cus-table">
              <el-table-column fixed align="center" type="index" label="序号" width="60" />
              <el-table-column fixed align="center" prop="orderCode" label="运维单号" width="200" />
              <el-table-column align="center" prop="stationCode" label="电站编码" width="200">
                <template #default="scope">
                  {{ scope.row.stationCode || '-' }}
                </template>
              </el-table-column>
              <el-table-column align="center" prop="inverterSn" label="逆变器SN码" width="150">
                <template #default="scope">
                  {{ scope.row.inverterSn || '-' }}
                </template>
              </el-table-column>
              <el-table-column align="center" prop="orderSource" label="工单来源" width="120">
                <template #default="scope">
                  {{ getDictLabel('work_order_source', scope.row.orderSource) }}
                </template>
              </el-table-column>
              <el-table-column align="center" prop="orderType" label="工单类型" width="120">
                <template #default="scope">
                  {{ getDictLabel('work_order_type', scope.row.orderType) }}
                </template>
              </el-table-column>
              <el-table-column align="center" prop="orderStatus" label="工单状态" width="120">
                <template #default="scope">
                  {{ getDictLabel('work_order_status', scope.row.orderStatus) }}
                </template>
              </el-table-column>
              <el-table-column align="center" prop="faultDescription" label="故障现象" width="150" />
              <el-table-column align="center" prop="mode" label="模式" width="120" />
              <el-table-column align="center" prop="specialFlag" label="资产所属" width="120">
                <template #default="scope">
                  {{ pmSpecialFlag[scope.row.specialFlag] || '-' }}
                </template>
              </el-table-column>
              <el-table-column align="center" prop="stationType" label="电站类型" width="120">
                <template #default="scope">
                  {{ detStationType[scope.row.stationType] || '-' }}
                </template>
              </el-table-column>
              <el-table-column align="center" prop="projectCompanyName" label="所属项目公司" width="150" />
              <el-table-column align="center" prop="opName" label="运维商" width="150" />
              <el-table-column align="center" prop="subCenterName" label="所属分中心" width="120" />
              <el-table-column align="center" prop="stationName" label="电站业主" width="120" />
              <el-table-column align="center" prop="stationPhone" label="业主联系方式" width="150" />
              <el-table-column align="center" prop="regionName" label="区域" width="120" />
              <el-table-column align="center" prop="address" label="详细地址" width="200" />
              <el-table-column align="center" prop="isWarranty" label="是否在质保期" width="120">
                <template #default="scope">
                  {{ isWarranty[scope.row.isWarranty] || '-' }}
                </template>
              </el-table-column>
              <el-table-column align="center" prop="opBusinessType" label="运维业务类型" width="150">
                <template #default="scope">
                  {{ businessType[scope.row.opBusinessType] || '-' }}
                </template>
              </el-table-column>
              <el-table-column fixed="right" align="center" label="操作" width="120">
                <template #default="scope">
                  <el-button link type="primary" @click="viewOrder(scope.row)">查看</el-button>
                  <el-button v-if="['TO_HEAD_DISPATCH', 'TO_SUB_CENTER_DISPATCH'].includes(scope.row.orderStatus)"
                    link type="primary">关单</el-button>
                </template>
              </el-table-column>
            </el-table>
            <el-pagination class="cus-pages" v-if="total" background layout="sizes, prev, pager, next, ->, total"
              :page-sizes="[10, 20, 30]" :page-size="pagination.pageSize" :current-page="pagination.pageNum"
              :total="total" @size-change="changeSize" @current-change="changeCurrent" />
          </div>
        </div>
      </div>
    </el-tab-pane>
  </el-tabs>
</template>

<script setup>
import { ref, onMounted, reactive, computed } from 'vue';
import { useRouter } from 'vue-router';
import API from '@/api/maintainance';
import _D from '@/edata/_osp_data';
import _ from 'lodash';
import { ArrowDown, ArrowUp } from '@element-plus/icons-vue';
import { useDictStore } from '@/stores/modules/dict';
import { useTablePagination } from '@/composables/useTablePagination';

const props = defineProps({
  stationCode: {
    type: String,
    required: true,
  },
});

const router = useRouter();

const pmSpecialFlag = _D.pmSpecialFlag;
const businessType = _D.businessType;
const isWarranty = _D.isWarranty;
const detStationType = _D.detStationType;

const isExpanded = ref(false);
const createTime = ref('');
const dictStore = useDictStore();

const sourceOptions = computed(() => dictStore.getDictByType('work_order_source'));
const workOrderTypeOptions = computed(() => dictStore.getDictByType('work_order_type'));

const getDictLabel = (dictType, value) => {
  const dictMap = dictStore.getDictMapByType(dictType);
  return dictMap[value] || '-';
};

const formSearch = reactive({
  createTimeEnd: null,
  createTimeStart: null,
  orderCode: '',
  stationCode: props.stationCode,
  orderSource: '',
  orderType: '',
});

const {
  loading,
  listArr,
  total,
  pagination,
  getList,
  queryList,
  changeSize,
  changeCurrent,
} = useTablePagination(
  API.getWorkOrderPage,
  () => formSearch,
  { manual: true }
);

const dateChange = (e) => {
  formSearch.createTimeStart = e ? e[0] : null;
  formSearch.createTimeEnd = e ? e[1] : null;
};

const onReset = _.throttle(
  () => {
    createTime.value = '';
    const stationCode = formSearch.stationCode;
    Object.assign(formSearch, {
      createTimeEnd: null,
      createTimeStart: null,
      orderCode: '',
      orderSource: '',
      orderType: '',
    });
    formSearch.stationCode = stationCode;
    queryList();
  },
  3000,
  { trailing: false }
);

const viewOrder = (row) => {
  router.push({
    path: '/maintainance/faultWorkOrder/detail',
    query: {
      orderCode: row.orderCode,
      action: 'view'
    }
  });
};

const toggleExpand = () => {
  isExpanded.value = !isExpanded.value;
};

onMounted(() => {
  getList();
  dictStore.fetchDict([
    'work_order_type',
    'work_order_source',
    'work_order_status',
  ]);
});
</script>

<style lang="less" scoped>
@import '@/assets/style/_cus_header.less';
@import '@/assets/style/_cus_list.less';

.section-card {
  margin-bottom: 20px;
  border-radius: 4px;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.05);
}

.wrap {
  height: 600px;
  padding: 0px;
}

.cus-header {
  margin-bottom: 0px;

  .form-item-grid {
    display: grid;
    grid-template-columns: repeat(4, 1fr);
    gap: 12px;
    width: 100%;

    .el-form-item:nth-child(n+4) {
      display: none;
    }

    &.is-expanded {
      .el-form-item:nth-child(n+4) {
        display: flex;
      }
    }
  }

  .search-buttons {
    display: flex;
    align-items: center;
    justify-content: flex-end;
    grid-column: -2 / -1;
  }

  .el-form-item {
    width: 100%;
    margin-bottom: 0px;

    .el-input,
    .el-select,
    .el-date-editor {
      width: 100%;
    }
  }
}

.cus-main {
  display: flex;
  flex-direction: column;
  flex: 1;
  overflow: hidden;
  margin-top: 10px;

  .cus-list {
    display: flex;
    flex-direction: column;
    flex: 1;
    overflow: hidden;
    position: relative;

    .cus-table {
      flex: 1;
      overflow: auto;
      height: 100%;
    }

    .cus-pages {
      margin-top: 10px;
    }
  }
}
</style>
