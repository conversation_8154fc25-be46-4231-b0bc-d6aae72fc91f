<template>
  <Modal
    v-model="dialogVisible"
    :title="dialogTitle"
    width="600"
    :mask-closable="false"
    @on-cancel="handleClose"
  >
    <Form ref="formRef" :model="form" :label-width="80">
      <div v-for="(mppt, mpptIndex) in form.mppts" :key="mpptIndex" class="mppt-block">
        <FormItem
          :label="`MPPT ${mpptIndex + 1}`"
          :prop="`mppts.${mpptIndex}.name`"
          :rules="{ required: true, message: 'MPPT名称不能为空', trigger: 'blur' }"
        >
          <Input v-model="mppt.name" placeholder="例如: mppt1" style="width: calc(100% - 100px); margin-right: 10px;" />
          <Button type="error" size="small" @click="removeMppt(mpptIndex)" v-if="form.mppts.length > 1">删除</Button>
        </FormItem>
        <div v-for="(pv, pvIndex) in mppt.pv" :key="pvIndex" class="pv-block">
          <FormItem
            :label="`PV ${getGlobalPvIndex(mpptIndex, pvIndex)}`"
            :prop="`mppts.${mpptIndex}.pv.${pvIndex}.name`"
            :rules="{ required: true, message: 'PV名称不能为空', trigger: 'blur' }"
          >
            <Input v-model="pv.name" placeholder="例如: pv1" style="width: 150px; margin-right: 10px;" />
            <FormItem
              label="数量"
              :label-width="50"
              :prop="`mppts.${mpptIndex}.pv.${pvIndex}.total`"
              :rules="{ type: 'number', min: 0, message: '数量必须为非负数', trigger: 'change' }"
              style="display: inline-flex; margin-bottom: 0;"
            >
              <InputNumber v-model="pv.total" :min="0" />
            </FormItem>
            <Button type="error" size="small" @click="removePv(mpptIndex, pvIndex)" v-if="mppt.pv.length > 1" style="margin-left: 10px;">删除</Button>
          </FormItem>
        </div>
        <Button @click="addPv(mpptIndex)" type="text" style="margin-left: 80px;">添加PV</Button>
      </div>
      <Button @click="addMppt" style="margin-top: 10px;">添加MPPT</Button>
    </Form>
    <div slot="footer">
      <Button @click="handleClose">取消</Button>
      <Button type="primary" @click="handleSubmit" :loading="isSubmitting">
        保存
      </Button>
    </div>
  </Modal>
</template>

<script>
import _ from 'lodash';

export default {
  name: 'StringDetailEditDialog',
  props: {
    visible: {
      type: Boolean,
      default: false
    },
    data: {
      type: Array,
      default: () => []
    }
  },
  data() {
    return {
      form: { mppts: [] },
      isSubmitting: false
    };
  },
  computed: {
    dialogVisible: {
      get() {
        return this.visible;
      },
      set(val) {
        this.$emit('update:visible', val);
      }
    },
    dialogTitle() {
      return this.data && this.data.length > 0 ? '编辑组串' : '添加组串';
    }
  },
  watch: {
    visible: {
      handler(newVal) {
        if (newVal) {
          this.initFormData();
        }
      },
      immediate: true
    }
  },
  methods: {
    // 计算全局PV索引
    getGlobalPvIndex(mpptIndex, pvIndex) {
      let globalIndex = 0;
      // 累加前面所有MPPT的PV数量
      for (let i = 0; i < mpptIndex; i++) {
        globalIndex += this.form.mppts[i].pv ? this.form.mppts[i].pv.length : 0;
      }
      // 加上当前PV的索引（从1开始）
      return globalIndex + pvIndex + 1;
    },

    // 初始化表单数据的函数
    initFormData() {
      const data = this.data || [];
      if (data.length === 0) {
        // 新增时提供默认结构
        this.form.mppts = [{
          name: 'mppt1',
          total: 0,
          pv: [{ name: 'pv1', total: 0 }]
        }];
      } else {
        // 编辑时使用现有数据
        this.form.mppts = _.cloneDeep(data);
      }
    },

    handleClose() {
      this.$emit('update:visible', false);
    },

    addMppt() {
      // 计算所有MPPT下的PV总数量
      const totalPvCount = this.form.mppts.reduce((count, mppt) => {
        return count + (mppt.pv ? mppt.pv.length : 0);
      }, 0);

      this.form.mppts.push({
        name: `mppt${this.form.mppts.length + 1}`,
        total: 0,
        pv: [{ name: `pv${totalPvCount + 1}`, total: 0 }]
      });
    },

    removeMppt(index) {
      this.form.mppts.splice(index, 1);
    },

    addPv(mpptIndex) {
      // 计算所有MPPT下的PV总数量
      const totalPvCount = this.form.mppts.reduce((count, mppt) => {
        return count + (mppt.pv ? mppt.pv.length : 0);
      }, 0);

      const pvList = this.form.mppts[mpptIndex].pv;
      pvList.push({
        name: `pv${totalPvCount + 1}`,
        total: 0
      });
    },

    removePv(mpptIndex, pvIndex) {
      const mppt = this.form.mppts[mpptIndex];
      if (mppt.pv.length > 1) {
        mppt.pv.splice(pvIndex, 1);
      } else {
        this.$Message.warning('每个MPPT至少需要保留一个PV');
      }
    },

    async handleSubmit() {
      if (!this.$refs.formRef) return;
      try {
        const valid = await this.$refs.formRef.validate();
        if (valid) {
          // 校验MPPT不能为空
          if (!this.form.mppts || this.form.mppts.length === 0) {
            this.$Message.warning('至少需要添加一个MPPT');
            return;
          }

          // 校验每个MPPT至少有一个PV
          const invalidMppt = this.form.mppts.find(mppt => !mppt.pv || mppt.pv.length === 0);
          if (invalidMppt) {
            this.$Message.warning('每个MPPT至少需要包含一个PV');
            return;
          }

          // 校验每个PV的数量不能为0
          for (const mppt of this.form.mppts) {
            for (const pv of mppt.pv) {
              if (!pv.total || pv.total <= 0) {
                this.$Message.warning(`${pv.name} 的数量不能为0，请输入有效数量`);
                return;
              }
            }
          }

          this.isSubmitting = true;
          const finalData = this.form.mppts.map(mppt => {
            const total = mppt.pv.reduce((sum, pv) => sum + (pv.total || 0), 0);
            return { ...mppt, total };
          });
          this.$emit('submit', finalData);
        } else {
          this.$Message.warning('请检查表单输入是否正确');
        }
      } catch (error) {
        console.error('表单验证失败', error);
      }
    },

    resetSubmitting() {
      this.isSubmitting = false;
    }
  }
};
</script>

<style lang="scss" scoped>
@import '@/style/_variables.scss';

.mppt-block {
  border: 1px solid $border-color-1;
  border-radius: 4px;
  padding: 15px;
  margin-bottom: 15px;
  position: relative;
  background: #fff;
}

.pv-block {
  margin-left: 20px;
  padding-left: 10px;
  border-left: 2px solid $border-color-2;
}
</style>
