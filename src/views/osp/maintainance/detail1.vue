<template>
    <div class="fault-detail-page">
        <Button type="text" icon="md-undo" @click="$router.go(-1)">返回</Button>
        <!-- 电站信息 -->
        <Card class="section-card">
            <p slot="title">电站信息</p>
            <Descriptions :column="2" bordered>
                <DescriptionItem label="电站编码">{{ formData.stationCode || '-' }}</DescriptionItem>
                <DescriptionItem label="逆变器SN码">
                    <a v-if="formData.inverterSn" type="text">
                        {{ formData.inverterSn }}
                    </a>
                    <span v-else>-</span>
                </DescriptionItem>
                <DescriptionItem label="业主姓名">{{ formData.stationName || '-' }}</DescriptionItem>
                <DescriptionItem label="手机号">{{ formData.stationPhone || '-' }}</DescriptionItem>
                <DescriptionItem label="电站模式">{{ formData.stationMode || '-' }}</DescriptionItem>
                <DescriptionItem label="关联资方">{{ formData.specialFlag || '-' }}</DescriptionItem>
                <DescriptionItem label="所属分中心">{{ formData.subCenterName || '-' }}</DescriptionItem>
                <DescriptionItem label="服务商类别">{{ formData.opType || '-' }}</DescriptionItem>
                <DescriptionItem label="运维商名称">{{ formData.opName || '-' }}</DescriptionItem>
                <!-- <DescriptionItem label="是否质保期内">{{ formData.isWarranty || '-' }}</DescriptionItem> -->
                <DescriptionItem label="区域">{{ (formData.provinceName || '') + (formData.cityName || '') +
                    (formData.regionName || '') || '-' }}</DescriptionItem>
                <DescriptionItem label="详细地址" :span="2">{{ formData.address || '-' }}</DescriptionItem>
            </Descriptions>
        </Card>

        <!-- 逆变器信息 -->
        <Card class="section-card">
            <p slot="title">电站信息</p>
            <Descriptions :column="2" bordered>
                <DescriptionItem label="电站编码">{{ formData.stationCode || '-' }}</DescriptionItem>
                <DescriptionItem label="逆变器SN码">
                    <a v-if="formData.inverterSn" type="text">
                        {{ formData.inverterSn }}
                    </a>
                    <span v-else>-</span>
                </DescriptionItem>
                <DescriptionItem label="业主姓名">{{ formData.stationName || '-' }}</DescriptionItem>
                <DescriptionItem label="手机号">{{ formData.stationPhone || '-' }}</DescriptionItem>
                <DescriptionItem label="电站模式">{{ formData.stationMode || '-' }}</DescriptionItem>
                <DescriptionItem label="关联资方">{{ formData.specialFlag || '-' }}</DescriptionItem>
                <DescriptionItem label="所属分中心">{{ formData.subCenterName || '-' }}</DescriptionItem>
                <DescriptionItem label="服务商类别">{{ formData.opType || '-' }}</DescriptionItem>
                <DescriptionItem label="运维商名称">{{ formData.opName || '-' }}</DescriptionItem>
                <!-- <DescriptionItem label="是否质保期内">{{ formData.isWarranty || '-' }}</DescriptionItem> -->
                <DescriptionItem label="区域">{{ (formData.provinceName || '') + (formData.cityName || '') +
                    (formData.regionName || '') || '-' }}</DescriptionItem>
                <DescriptionItem label="详细地址" :span="2">{{ formData.address || '-' }}</DescriptionItem>
            </Descriptions>
        </Card>
    </div>
</template>

<script>
import API from '@/api/maintainance';
import { mapActions, mapGetters } from 'vuex';
import Descriptions from '../components/Descriptions.vue';
import DescriptionItem from '../components/DescriptionItem.vue';

export default {
    name: 'FaultDetail',
    components: {
        Descriptions,
        DescriptionItem,
    },
    data() {
        return {
            selectStaffModalVisible: false, // 控制 SelectStaff 弹窗的显示状态
            rejectOrderDialogVisible: false,
            previewImageVisible: false,
            previewImageUrl: '',
            action: this.$route.query.action,
            activeTabName: 'processInfo',
            formData: {
                address: '',
                auditMode: '',
                auditUnit: '',
                businessType: '',
                cityId: '',
                cityName: '',
                closeReason: '',
                closeTime: '',
                configCheckItems: [],
                configId: '',
                createdAt: '',
                createdBy: '',
                deadline: '',
                devices: {},
                dispatchAuditPermission: '',
                dispatchMode: '',
                dispatchUnit: '',
                dispatched: null,
                faultDescription: '',
                faultInfo: {},
                finishFlag: '',
                finishTime: '',
                firstAuditResult: '',
                handleCheckItems: [],
                handleTime: '',
                handler: '',
                id: '',
                inverterSn: '',
                isWarranty: null,
                opCode: '',
                opMemberId: '',
                opName: '',
                opType: '',
                orderCode: '',
                orderName: '',
                orderSource: '',
                orderStatus: '',
                orderType: '',
                overTime: null,
                processes: [],
                provinceId: '',
                provinceName: '',
                regionId: '',
                regionName: '',
                remark: '',
                secondAuditResult: '',
                sparePartApplyNo: '',
                sparePartAuditPassTime: '',
                sparePartAuditStatus: '',
                specialFlag: '',
                stationCode: '',
                stationMode: '',
                stationName: '',
                stationPhone: '',
                streetId: '',
                streetName: '',
                subCenterName: '',
            },
            handleFormRules: {}, // 初始化表单校验规则
        };
    },
    computed: {
        ...mapGetters('dict/maintainance', [
            'getDictByType',
            'getDictMapByType'
        ]),
    },
    methods: {
        ...mapActions('dict/maintainance', {
            fetchMaintainanceDict: 'fetchDict'
        }),
        getDictLabel(dictType, value) {
            const dictMap = this.getDictMapByType(dictType);
            return dictMap ? (dictMap[value] || '-') : '-';
        },
        async getDetail() {
            const stationCode = this.$route.query.stationCode;
            if (!orderCode) {
                this.$Message.error('缺少电站标识');
                this.$router.back();
                return;
            }
            this.formData.stationCode = stationCode;

            try {
                const res = (await API.getWorkOrderByOrderCode(orderCode)).data;
                if (res.success) {
                    Object.assign(this.formData, res.result);
                    if (!['TO_HEAD_DISPATCH', 'TO_SUB_CENTER_DISPATCH', 'TO_ASSIGN', 'CLOSED'].includes(this.formData.orderStatus)) {
                        this.activeTabName = 'workOrderHandle';
                    } else {
                        this.activeTabName = 'processInfo';
                    }
                    if (Array.isArray(this.formData.handleCheckItems) && this.formData.handleCheckItems.length === 0) {
                        this.formData.handleCheckItems = this.formData.configCheckItems?.map(item => ({ ...item, resultContent: item.resultType === 'image' ? [] : null })) || [];
                    } else {
                        this.formData.handleCheckItems = this.formData.handleCheckItems?.map(item => ({ ...item, resultContent: item.resultType === 'image' ? item.resultContent.split(',') : item.resultContent })) || [];
                    }
                    if (this.formData.createdAt) {
                        this.formData.createdAt = this.formData.createdAt.replace('T', ' ');
                    }
                } else {
                    this.$Message.error(res.error || '获取详情失败');
                }
            } catch (error) {
                console.error('获取详情失败:', error);
                this.$Message.error('获取详情数据时发生错误');
            }
        },
    },
    created() {
        this.getDetail();
        this.fetchMaintainanceDict([
            'fault_level',
            'work_order_type',
            'work_order_status',
            'dispatch_mode',
            'dispatch_review_permission',
            'review_mode',
            'work_order_source',
        ]);
    }
};
</script>

<style scoped lang="scss">
.fault-detail-page {
    background-color: white;
    padding: 16px;
}

.cus-back {
    margin-bottom: 16px;
}

.section-card {
    margin-bottom: 16px;
}

.card-header {
    font-weight: bold;
}

.action-buttons {
    text-align: center;
    margin-top: 20px;

    .ivu-btn {
        margin-left: 8px;
    }
}

.image-slot {
    display: flex;
    justify-content: center;
    align-items: center;
    width: 100%;
    height: 100%;
    background: #f5f7fa;
    color: #909399;
    font-size: 14px;
}

.time {
    font-size: 14px;
    font-weight: bold;
}

.content {
    padding-left: 5px;
}

.flex-column {
    display: flex;
    flex-direction: column;
    gap: 8px;
}
</style>
