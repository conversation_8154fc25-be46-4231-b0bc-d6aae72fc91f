import {
  get,
  post
} from "@/axios/http";

export default {

  // 获取字典
  getMaintainanceDict: (dictCode) => {
    return get(`/merchant/light/operation/dict/items/${dictCode}`);
  },
  getMaintainanceDicts: (dictCodes) => {
    return post(`/merchant/light/operation/dict/items/batch`, dictCodes, 1);
  },

  // 方案管理
  // 获取方案分类列表
  getSolutionCategoryList: () => {
    return get("/merchant/light/operation/solution/category/list");
  },
  // 获取方案详情
  getSolution: (params) => {
    return get("/merchant/light/operation/solution/get", params);
  },
  // 分页查询方案列表
  getSolutionList: (params) => {
    return get("/merchant/light/operation/solution/list", params);
  },
  // 分页查询方案列表
  getSolutionPage: (params) => {
    return get("/merchant/light/operation/solution/page", params, 1);
  },


  // 工单管理
  // 关闭工单
  closeWorkOrder: (params) => {
    return post(`/merchant/light/operation/workOrder/close`, params, 1);
  },
  // 获取工单详情
  getWorkOrderConfigList: () => {
    return get(`/merchant/light/operation/workOrder/config/list`);
  },
  // 获取工单详情
  getWorkOrderDetail: (id) => {
    return get(`/merchant/light/operation/workOrder/get/${id}`);
  },
  // 根据运维单号获取工单详情
  getWorkOrderByOrderCode: (orderCode) => {
    return get(`/merchant/light/operation/workOrder/getByOrderCode/${orderCode}`);
  },
  // 处理工单
  handleWorkOrder: (params) => {
    return post(`/merchant/light/operation/workOrder/handle`, params, 1);
  },
  // 分页查询工单列表
  getHandledWorkOrderPage: (params) => {
    return get("/merchant/light/operation/workOrder/handled/page", params);
  },
  // 工单列表
  getWorkOrderList: (params) => {
    return get("/merchant/light/operation/workOrder/list", params);
  },
  // 分页查询我的提报工单列表
  getMySubmittedWorkOrderPage: (params) => {
    return get("/merchant/light/operation/workOrder/my-submitted", params);
  },
  // 分页查询工单列表
  getWorkOrderPage: (params) => {
    return get("/merchant/light/operation/workOrder/page", params);
  },
  // 获取在职员工列表
  getWorkOrderStaffList: () => {
    return get("/merchant/light/operation/workOrder/staffList");
  },
  // 指派工单
  assignWorkOrder: (params) => {
    return post(`/merchant/light/operation/workOrder/assign`, params, 1);
  },
  // 提报工单
  submitWorkOrder: (params) => {
    return post(`/merchant/light/operation/workOrder/submit`, params, 1);
  },

  // 根据工单编号获取巡检工单
  getInspectionWorkOrder: (orderCode) => {
    return get(`/merchant/light/operation/inspection/work-order/get`, { orderCode });
  },

  // 处理巡检工单
  handleInspectionWorkOrder: (params) => {
    return post(`/merchant/light/operation/inspection/work-order/handle`, params, 1);
  },

  // 获取巡检工单列表
  getInspectionWorkOrderList: (params) => {
    return get(`/merchant/light/operation/inspection/work-order/list`, params);
  },

  // 获取我的已处理工单列表
  getMyHandledWorkOrderList: () => {
    return get(`/merchant/light/operation/inspection/work-order/my-handled`);
  },

  // 分页查询巡检工单列表
  getInspectionWorkOrderPage: (params) => {
    return get(`/merchant/light/operation/inspection/work-order/page`, params);
  },

  // 分页查询巡检工单列表
  getInspectionWorkOrderStatistics: (params) => {
    return get(`/merchant/light/operation/inspection/work-order/statistics`, params);
  },

  // 获取待处理巡检工单列表
  getToHandleWorkOrderList: () => {
    return get(`/merchant/light/operation/inspection/work-order/to-handle`);
  },

  // 电站列表
  // 分页查询电站列表
  getStationPage: (params) => {
    return get("/merchant/light/operation/station/page", params);
  },
  // 根据电站编码获取电站详情
  getStationByStationCode: (params) => {
    return get("/merchant/light/operation/station/getByStationCode", params);
  },
  // 获取逆变器实时数据列表
  getStationInverterData: (params) => {
    return get("/merchant/light/operation/station/inverter/data", params);
  },
  // 获取逆变器发电量数据
  getStationInverterElecData: (params) => {
    return get("/merchant/light/operation/station/inverter/elec-data", params);
  },
  // 获取电站逆变器列表
  getStationInverterList: (params) => {
    return get("/merchant/light/operation/station/inverter/list", params);
  },
  // 获取逆变器MPPT数据列表
  getStationInverterMpptData: (params) => {
    return get("/merchant/light/operation/station/inverter/mppt-data", params);
  },

  // 报表大厅
  // 获取报表配置详情
  getReportConfig: (params) => {
    return get(`/merchant/light/operation/report-center/config/details`, params);
  },
  // 根据报表配置获取报表
  getReportCenterConfigData: (params) => {
    return get('/merchant/light/operation/report-center/get-by-config', params);
  },
  // 获取当前用户的报表配置列表
  getReportCenterConfigList: () => {
    return get('/merchant/light/operation/report-center/my-config-list');
  },

  // 消息中心
  // 1. 批量标记消息已读
  batchMarkReadMessage: (params) => {
    return post("/merchant/light/operation/message/batch-mark-read", params, 1);
  },
  // 2. 根据ID获取消息详情
  getMessageDetail: (params) => {
    return get("/merchant/light/operation/message/detail", params);
  },
  // 3. 标记消息已读
  markMessageRead: (params) => {
    return post("/merchant/light/operation/message/mark-read", params);
  },
  // 4. 分页查询消息列表
  getMessagePage: (params) => {
    return get("/merchant/light/operation/message/page", params);
  },
  // 5. 获取未读消息数量
  getUnreadMessageCount: (params) => {
    return get("/merchant/light/operation/message/unread-count", params);
  },
}
