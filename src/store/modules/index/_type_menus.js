export default [
    // {
    //   name: "账户管理",
    //   path: "/osp",
    //   showChild: 0,
    //   tabType: 81,
    //   type: "light_osp",
    //   child: [
    //     {
    //       name: "备件保证金",
    //       path: "/osp/depositList",
    //       signColor: 810,
    //       type: "light_osp",
    //       child: 1,
    //     },
    //   ],
    // },
    {
        name: "工单大厅",
        path: "/osp",
        showChild: 0,
        tabType: 206,
        type: "light_osp,light_osp_warranty",
        child: [{
                name: "运维工单",
                path: "/osp/maintainance/list",
                signColor: 810,
                type: "light_osp,light_osp_warranty",
            },
            {
                name: "备件申请",
                path: "/osp/maintainance/apply",
                signColor: 810,
                type: "light_osp,light_osp_warranty",
            },
            {
                name: "提报工单",
                path: "/osp/maintainance/my",
                signColor: 820,
                type: "light_osp,light_osp_warranty",
            },
        ],
    },
    {
        name: "方案库",
        path: "/osp",
        showChild: 0,
        tabType: 206,
        type: "light_osp,light_osp_warranty",
        child: [{
                name: "方案列表",
                path: "/osp/maintainance/solution/list",
                signColor: 830,
                type: "light_osp,light_osp_warranty",
            }
        ],
    },
    {
        name: "巡检管理",
        path: "/osp",
        showChild: 0,
        tabType: 206,
        type: "light_osp,light_osp_warranty",
        child: [{
                name: "巡检计划",
                path: "/osp/maintainance/inspect/plan/list",
                signColor: 830,
                type: "light_osp,light_osp_warranty",
            },{
                name: "巡检任务",
                path: "/osp/maintainance/inspect/task/list",
                signColor: 830,
                type: "light_osp,light_osp_warranty",
            }
        ],
    },
    {
        name: "电站中心",
        path: "/osp",
        showChild: 0,
        tabType: 206,
        type: "light_osp,light_osp_warranty",
        child: [{
                name: "电站列表",
                path: "/osp/maintainance/station/index",
                signColor: 830,
                type: "light_osp,light_osp_warranty",
            }
        ],
    },
    {
        name: "报表大厅",
        path: "/osp",
        showChild: 0,
        tabType: 206,
        type: "light_osp,light_osp_warranty",
        child: [{
                name: "报表管理",
                path: "/osp/maintainance/report/index",
                signColor: 830,
                type: "light_osp,light_osp_warranty",
            }
        ],
    },
    {
        name: "消息管理",
        path: "/osp",
        showChild: 0,
        tabType: 206,
        type: "light_osp,light_osp_warranty",
        child: [{
                name: "消息管理",
                path: "/osp/maintainance/message/index",
                signColor: 830,
                type: "light_osp,light_osp_warranty",
            }
        ],
    },
    {
        name: "基础管理",
        path: "/osp",
        showChild: 0,
        tabType: 81,
        type: "light_osp,light_osp_warranty",
        child: [{
                name: "公司合同管理",
                path: "/osp/ospContract",
                signColor: 810,
                type: "light_osp,light_osp_warranty",
            },
            {
                name: "服务区域",
                path: "/osp/ospRegion",
                signColor: 811,
                type: "light_osp,light_osp_warranty",
            },
            {
                name: "电签认证",
                path: "/osp/ospRegister",
                signColor: 812,
                type: "light_osp,light_osp_warranty",
            },
            {
                name: "服务资质管理",
                path: "/osp/serviceQualification",
                signColor: 813,
                type: "light_osp,light_osp_warranty",
            }
        ],
    },
    {
        name: "运维管理",
        path: "/osp",
        showChild: 0,
        tabType: 82,
        type: "light_osp,light_osp_warranty",
        child: [{
                name: "运维工单",
                path: "/osp/operationList",
                signColor: 820,
                type: "light_osp,light_osp_warranty",
            },
            {
                name: "运维知识库",
                path: "/osp/knowledge",
                signColor: 821,
                type: "light_osp,light_osp_warranty",
            },
            {
                name: "电站数据变更",
                path: "/osp/pStationChanges",
                signColor: 822,
                type: "light_osp,light_osp_warranty",
            },
            {
                name: "电站列表",
                path: "/osp/powerStation",
                signColor: 823,
                type: "light_osp,light_osp_warranty",
            },
            {
                name: "逆变器列表",
                path: "/osp/operationInverter",
                signColor: 825,
                type: "light_osp,light_osp_warranty",
            },
            {
                name: "电站租金查询",
                path: "/osp/rentQuery",
                signColor: 826,
                type: "light_osp,light_osp_warranty",
            },
        ],
    },
    {
        name: "服务人员",
        path: "/osp",
        showChild: 0,
        tabType: 83,
        type: "light_osp,light_osp_warranty",
        child: [{
                name: "人员管理",
                path: "/osp/personnelList",
                signColor: 830,
                type: "light_osp,light_osp_warranty",
            },
            {
                name: "上岗资料管理",
                path: "/osp/jobQualification",
                signColor: 831,
                type: "light_osp,light_osp_warranty",
            },
        ],
    },
    {
        name: "结算管理",
        path: "/osp",
        showChild: 0,
        tabType: 84,
        type: "light_osp",
        child: [{
                name: "结算账单(户用)",
                path: "/osp/settleBills",
                signColor: 840,
                type: "light_osp",
            },
            {
                name: "结算账单(工商业)",
                path: "/osp/oamSettleBills",
                signColor: 841,
                type: "light_osp",
            },
            {
                name: "特殊费用列表(户用)",
                path: "/osp/specialCharges",
                signColor: 843,
                type: "light_osp",
            },
            {
                name: "运维商政策兑现+(户用)",
                path: "/osp/settPositiveMotivation",
                signColor: 845,
                type: "light_osp",
            },
            {
                name: "运维商政策兑现-(户用)",
                path: "/osp/settNegativeIncentive",
                signColor: 846,
                type: "light_osp",
            },
            {
                name: "运维商政策兑现+(工商业)",
                path: "/osp/oamPositiveMotivation",
                signColor: 847,
                type: "light_osp",
            },
            {
                name: "运维商政策兑现-(工商业)",
                path: "/osp/oamNegativeIncentive",
                signColor: 848,
                type: "light_osp",
            },
        ],
    },
    {
        name: "账户管理",
        path: "/osp",
        showChild: 0,
        tabType: 85,
        type: "light_osp,light_osp_warranty",
        child: [{
            name: "运维保证金",
            path: "/osp/ospMargin",
            signColor: 850,
            type: "light_osp,light_osp_warranty",
            child: 1
        }, ],
    },
    {
        name: "订单管理",
        path: "/osp",
        showChild: 0,
        tabType: 86,
        type: "light_osp",
        child: [{
                name: "服务商订单",
                path: "/osp/serviceProviderOrder",
                signColor: 2000,
                type: "light_osp",
            },
            {
                name: "借件订单",
                path: "/osp/lendOrder",
                signColor: 2001,
                type: "light_osp",
            },
            {
                name: "借件订单-特殊来源",
                path: "/osp/lendOrderSpecial",
                signColor: 2002,
                type: "light_osp_borrow",
            },
            {
                name: "服务商调拨",
                path: "/osp/serviceAllot",
                signColor: 2004,
                type: "light_osp",
            },
            {
                name: "服务商调拨-确认",
                path: "/osp/serviceAllotConfirm",
                signColor: 2006,
                type: "light_osp",
            },
        ],
    },
    {
        name: "入库管理",
        path: "/osp",
        showChild: 0,
        tabType: 201,
        type: "light_osp",
        child: [{
                name: "服务商入库",
                path: "/osp/serviceStock",
                signColor: 2010,
                type: "light_osp",
                child: 1,
            },
            {
                name: "货损货差列表-服务商",
                path: "/osp/damageList",
                signColor: 2013,
                type: "light_osp",
                child: 1,
            },
            {
                name: "工程师备件入库",
                path: "/osp/engineerStock",
                signColor: 2014,
                type: "light_osp",
                child: 1,
            }
        ]
    },
    {
        name: "出库管理",
        path: "/osp",
        showChild: 0,
        tabType: 202,
        type: "light_osp",
        child: [{
                name: "退返出库",
                path: "/osp/returnStockOut",
                signColor: 2020,
                type: "light_osp",
                child: 1,
            },
            {
                name: "快递路由查询",
                path: "/osp/expressCheck",
                signColor: 2022,
                type: "light_osp",
                child: 1,
            },
            {
                name: "服务商出库",
                path: "/osp/serviceStockOut",
                signColor: 2023,
                type: "light_osp",
                child: 1,
            },
        ]
    },
    {
        name: "库存管理",
        path: "/osp",
        showChild: 0,
        tabType: 203,
        type: "light_osp",
        child: [{
                name: "库存调整",
                path: "/osp/repertoryResize",
                signColor: 2030,
                type: "light_osp",
                child: 1,
            },
            {
                name: "安全库存设置",
                path: "/osp/repertorySet",
                signColor: 2032,
                type: "light_osp",
                child: 1,
            },
            {
                name: "现有库存查看-服务商",
                path: "/osp/currentRepertoryViewService",
                signColor: 2033,
                type: "light_osp",
                child: 1,
            },
        ]
    },
    {
        name: "财务管理",
        path: "/osp",
        showChild: 0,
        tabType: 204,
        type: "light_osp",
        child: [{
                name: "备件保证金",
                path: "/osp/advanceManage",
                signColor: 2040,
                type: "light_osp",
                child: 1,
            },
            {
                name: "账单流水查询",
                path: "/osp/billFlowQuery",
                signColor: 2041,
                type: "light_osp",
                child: 1,
            },
            {
                name: "运维商备件回购",
                path: "/osp/buybackManage",
                signColor: 2042,
                type: "light_osp",
                child: 1,
            }
        ]
    },
    {
        name: "保险管理",
        path: "/osp",
        showChild: 0,
        tabType: 205,
        type: "light_osp",
        child: [{
                name: "保单号查询",
                path: "/osp/policyNumber",
                signColor: 2050,
                type: "light_osp",
                child: 1,
            },
            {
                name: "保险报案(财产一切险)",
                path: "/osp/property",
                signColor: 2051,
                type: "light_osp",
                child: 1,
            },
            {
                name: "保险报案(公众责任险)",
                path: "/osp/public",
                signColor: 2052,
                type: "light_osp",
                child: 1,
            },
        ]
    }
];
