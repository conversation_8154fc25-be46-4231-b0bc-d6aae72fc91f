# 编写代码规则

## 接口
- 接口在api/maintainance.js中定义。
- 引用通过import API from '@/api/maintainance'引用
- 调用通过API.xxx()调用
```
- 请求返回的格式
```
{
  data: {
    "result": [],
    "error": null,
    "message": null,
    "errorMap": {},
    "success": true
  }
}
```
- 分页请求返回的格式
```
{
  "result": {
    content: [],
    totalPages: 0
  },
  "error": null,
  "message": null,
  "errorMap": {},
  "success": true
}
```
- 接口返回的result是数组，error是错误信息，message是消息，errorMap是错误信息的映射，success是是否成功

## 字典使用规则

项目中的字典数据通过 Vuex 进行统一管理和获取。

### 1. Vuex Store 结构 (示例)
- 字典相关的 Vuex 模块通常位于 `src/store/modules/dict/` 下，例如 `src/store/modules/dict/maintainance.js`。
- **State**: 存储从后端获取的原始字典数据。
- **Getters**:
    - `getDictByType(state)(type)`: 根据字典类型名称返回一个包含 `{ label, value }` 对象的数组，常用于下拉选择框。
    - `getDictMapByType(state)(type)`: 根据字典类型名称返回一个以字典 `value` 为键，`label` 为值的对象映射，常用于快速查找显示标签。
- **Actions**:
    - `fetchDict(context, dictTypesArray)`: 异步从后端API获取指定类型的字典数据，并存入 state。通常在组件的 `mounted` 钩子中调用。
- **Mutations**: 用于更新 state 中的字典数据。

### 2. 在组件中获取和使用字典

#### 2.1 获取字典数据 (通常在 `mounted` 钩子)
```javascript
// 引入 Vuex actions
import { mapActions } from 'vuex';

export default {
  // ...
  methods: {
    ...mapActions('dict/maintainance', { // 'dict/maintainance' 是 Vuex 模块的命名空间
      fetchMaintainanceDict: 'fetchDict' // 将 'fetchDict' action 映射为 'fetchMaintainanceDict' 方法
    }),
    // ...
  },
  mounted() {
    this.fetchMaintainanceDict([
      'work_order_type',      // 工单类型字典
      'work_order_status',    // 工单状态字典
      'work_order_source',    // 工单来源字典
      // ... 其他需要的字典类型
    ]);
  }
}
```

#### 2.2 在 `<script>` 中使用 Getter
```javascript
// 引入 Vuex getters
import { mapGetters } from 'vuex';

export default {
  // ...
  computed: {
    ...mapGetters('dict/maintainance', [ // 'dict/maintainance' 是 Vuex 模块的命名空间
      'getDictByType',    // 获取字典数组的方法
      'getDictMapByType'  // 获取字典映射对象的方法
    ]),

    // 示例：为下拉框提供选项
    workOrderTypeOptions() {
      // 做非空判断，确保 getter 存在后再调用
      return this.getDictByType ? this.getDictByType('work_order_type') : [];
    },
    sourceOptions() {
      return this.getDictByType ? this.getDictByType('work_order_source') : [];
    },
    // ... 其他字典选项的计算属性
  },
  methods: {
    // 示例：根据字典值获取字典标签，用于表格等显示
    getDictLabel(dictType, value) {
      const dictMap = this.getDictMapByType(dictType);
      // 如果找不到对应的标签，可以返回原始值或占位符 '-'
      return dictMap ? (dictMap[value] || value || '-') : (value || '-');
    }
  }
}
```

#### 2.3 在 `<template>` 中使用

**用于下拉选择框 (Select):**
```html
<FormItem label="工单类型">
  <Select v-model="formSearch.orderType" placeholder="选择工单类型" clearable style="width: 100%;">
    <Option v-for="item in workOrderTypeOptions" :key="item.value" :value="item.value">{{ item.label }}</Option>
  </Select>
</FormItem>
```

**用于表格列中显示标签 (Table):**
```html
<Table :data="listArr" :columns="tableColumns">
  <template slot="orderType" slot-scope="{ row }">
    <span>{{ getDictLabel('work_order_type', row.orderType) }}</span>
  </template>
  <template slot="orderStatus" slot-scope="{ row }">
    <span>{{ getDictLabel('work_order_status', row.orderStatus) }}</span>
  </template>
</Table>
```

### 3. 注意事项
- 确保在使用 `getDictByType` 或 `getDictMapByType` 之前，相关的字典数据已经被 `fetchDict` action 成功加载。
- 在计算属性或方法中使用 getter 时，建议进行空判断 (如 `this.getDictByType ? ... : []`)，以防止在 Vuex store 初始化完成前访问导致错误。
- 字典类型名称 (如 `'work_order_type'`) 必须与后端定义和 Vuex store 中管理的一致。
- `getDictLabel` 方法中的回退逻辑 (当找不到标签时显示什么) 可以根据实际需求调整。
