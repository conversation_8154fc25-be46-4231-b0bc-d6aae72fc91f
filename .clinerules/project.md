# 项目规则

## 1. 技术栈

- **核心框架**: Vue.js 2.7.14
- **路由**: Vue Router 3.6.5
- **状态管理**: Vuex 3.0.1
- **UI 组件库**: View Design (iView) 4.7.0
- **HTTP 请求**: Axios 1.6.0
- **微前端**: Qiankun 2.10.16
- **CSS 预处理器**: Sass (SCSS)
- **构建工具**: Vue CLI 4.0.0

## 2. 代码规范

### 2.1 JavaScript/Vue

- **ESLint**: 项目已配置 ESLint，遵循 `plugin:vue/essential` 规则集，并包含以下自定义规则：
    - `no-console`: 允许使用 `console`。
    - `no-debugger`: 生产环境禁止使用 `debugger`，开发环境允许。
    - `quotes`: 关闭单引号强制规则。
    - `comma-dangle`: 不允许对象中出现结尾逗号。
    - `space-before-function-paren`: 关闭函数名与括号间必须有空格的规则。
    - `one-var`: 关闭 `var` 声明，每个声明占一行的规则。
    - `semi`: 关闭强制结尾分号的规则。
    - `indent`: 关闭强制缩进风格的规则。
    - `no-irregular-whitespace`: 关闭不规则空白字符检查。
    - `no-multi-spaces`: 关闭多个空格检查。
    - `eol-last`: 关闭文件末尾需要换行符的规则。
    - `vue/no-parsing-error`: 关闭标签末端校验，以处理 UI 组件可能引起的误报 (针对 `x-invalid-end-tag`)。
- **模块导入**: 使用 ES6 模块导入/导出。
- **命名规范**:
    - 组件名: PascalCase (例如 `MyComponent.vue`)
    - JavaScript 变量和函数名: camelCase (例如 `myVariable`, `myFunction`)
    - CSS 类名: kebab-case (例如 `my-class`)

### 2.2 CSS/SCSS

- **全局样式**: 项目已全局引入以下 SCSS 文件，请在编写样式时利用它们：
    - `@/style/_mixin.scss` (通用混合)
    - `@/style/_variables.scss` (颜色、字体等变量)
    - `@/style/_flex.scss` (Flexbox 布局辅助)
- **作用域**: 推荐在 Vue 组件中使用 `<style scoped>` 来限制样式作用域，避免全局污染。
- **单位**: 根据项目配置，可能使用了 `postcss-px2rem` 进行单位转换，请注意设计稿与实际像素的对应关系。

## 3. 项目结构

- **源代码**: 位于 `src/` 目录下。
- **组件**: 位于 `src/components/` 目录下，按功能或模块组织。
- **视图/页面**: 位于 `src/views/` 目录下。
- **API 请求**: 位于 `src/api/` 目录下。
- **状态管理 (Vuex)**: 位于 `src/store/` 目录下。
- **路由配置**: 位于 `src/router/` 目录下。
- **静态资源**: 位于 `src/assets/` (图片、图标等) 和 `public/` (如 `index.html`, `favicon.ico`)。
- **工具函数**: 位于 `src/utils/` 目录下。

## 4. 构建与部署

- **开发环境启动**: `npm run serve`
- **生产环境预览**: `npm run preview`
- **生产环境构建**: `npm run build`
- **开发环境构建**: `npm run dev-build`
- **代码检查**: `npm run lint`
- **构建分析**: `npm run analyzer`
- **公共路径**: `/sub/${process.env.VUE_APP_SUBNAME}`
- **代码压缩**: 生产构建时会使用 `CompressionWebpackPlugin` (gzip) 和 `TerserPlugin` (移除 console, debugger, 注释，代码混淆)。

## 5. 微前端 (Qiankun)

- 项目作为 Qiankun 微应用进行构建。
- Webpack 输出配置为 UMD 格式，库名为 `nahui-pv.merchant-micro.zch-[name]`。
- `jsonpFunction` (或 `chunkLoadingGlobal` for Webpack 5) 设置为 `webpackJsonp_nahui-pv.merchant-micro.zch`。

## 6. 别名配置

Webpack 中配置了以下路径别名，方便导入模块：

- `assets`: `@/assets`
- `components`: `@/components`
- `views`: `@/views`
- `style`: `@/style`
- `api`: `@/api`
- `store`: `@/store`
- `filters`: `@/filters`
- `utils`: `@/utils`
- `router`: `@/router`

## 7. 其他

- **依赖管理**: 使用 `package.json` 和 `npm`。
- **版本控制**: 使用 Git (参考 `.gitignore` 文件)。
- **编辑器配置**: 参考 `.editorconfig` 和 `.prettierrc.json` (如果存在并使用)。
- **注释**: 不添加没有必要的注释，特别是步骤性的注释或者更改的注释

请遵循以上规则进行项目开发，以确保代码质量和团队协作效率。